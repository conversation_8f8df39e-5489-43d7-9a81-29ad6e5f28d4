{"name": "Codeable - Coding Interview Platform", "short_name": "Codeable", "description": "Professional coding interview preparation platform with advanced editor and complexity analysis", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#3A29FF", "orientation": "portrait-primary", "scope": "/", "lang": "en", "categories": ["education", "productivity", "developer"], "icons": [{"src": "/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}], "shortcuts": [{"name": "New Workspace", "short_name": "New", "description": "Create a new coding workspace", "url": "/dashboard?action=new-workspace", "icons": [{"src": "/icon-96x96.png", "sizes": "96x96"}]}, {"name": "Practice Problems", "short_name": "Practice", "description": "Browse coding challenges", "url": "/dashboard?tab=challenges", "icons": [{"src": "/icon-96x96.png", "sizes": "96x96"}]}], "screenshots": [{"src": "/screenshot-wide.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "Codeable editor interface"}, {"src": "/screenshot-narrow.png", "sizes": "750x1334", "type": "image/png", "form_factor": "narrow", "label": "Codeable mobile interface"}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}