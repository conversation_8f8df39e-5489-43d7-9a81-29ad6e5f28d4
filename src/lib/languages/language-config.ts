// Language configuration and multi-language support

export interface LanguageConfig {
  id: string;
  name: string;
  displayName: string;
  fileExtension: string;
  monacoLanguage: string;
  supportsExecution: boolean;
  defaultTemplate: string;
  commentSyntax: {
    line: string;
    blockStart?: string;
    blockEnd?: string;
  };
  features: {
    formatting: boolean;
    linting: boolean;
    testing: boolean;
    debugging: boolean;
  };
}

// Supported languages configuration
export const LANGUAGE_CONFIGS: Record<string, LanguageConfig> = {
  javascript: {
    id: 'javascript',
    name: 'JavaScript',
    displayName: 'JavaScript',
    fileExtension: '.js',
    monacoLanguage: 'javascript',
    supportsExecution: true,
    defaultTemplate: `// JavaScript code
function solution() {
  // Your code here
  console.log('Hello, World!');
}

solution();`,
    commentSyntax: {
      line: '//',
      blockStart: '/*',
      blockEnd: '*/',
    },
    features: {
      formatting: true,
      linting: true,
      testing: true,
      debugging: true,
    },
  },

  typescript: {
    id: 'typescript',
    name: 'TypeScript',
    displayName: 'TypeScript',
    fileExtension: '.ts',
    monacoLanguage: 'typescript',
    supportsExecution: true,
    defaultTemplate: `// TypeScript code
function solution(): void {
  // Your code here
  console.log('Hello, World!');
}

solution();`,
    commentSyntax: {
      line: '//',
      blockStart: '/*',
      blockEnd: '*/',
    },
    features: {
      formatting: true,
      linting: true,
      testing: true,
      debugging: true,
    },
  },

  python: {
    id: 'python',
    name: 'Python',
    displayName: 'Python',
    fileExtension: '.py',
    monacoLanguage: 'python',
    supportsExecution: true,
    defaultTemplate: `# Python code
def solution():
    # Your code here
    print("Hello, World!")

if __name__ == "__main__":
    solution()`,
    commentSyntax: {
      line: '#',
      blockStart: '"""',
      blockEnd: '"""',
    },
    features: {
      formatting: true,
      linting: true,
      testing: true,
      debugging: false,
    },
  },

  java: {
    id: 'java',
    name: 'Java',
    displayName: 'Java',
    fileExtension: '.java',
    monacoLanguage: 'java',
    supportsExecution: false,
    defaultTemplate: `// Java code
public class Solution {
    public static void main(String[] args) {
        // Your code here
        System.out.println("Hello, World!");
    }
}`,
    commentSyntax: {
      line: '//',
      blockStart: '/*',
      blockEnd: '*/',
    },
    features: {
      formatting: true,
      linting: false,
      testing: false,
      debugging: false,
    },
  },

  cpp: {
    id: 'cpp',
    name: 'C++',
    displayName: 'C++',
    fileExtension: '.cpp',
    monacoLanguage: 'cpp',
    supportsExecution: false,
    defaultTemplate: `// C++ code
#include <iostream>
using namespace std;

int main() {
    // Your code here
    cout << "Hello, World!" << endl;
    return 0;
}`,
    commentSyntax: {
      line: '//',
      blockStart: '/*',
      blockEnd: '*/',
    },
    features: {
      formatting: true,
      linting: false,
      testing: false,
      debugging: false,
    },
  },

  c: {
    id: 'c',
    name: 'C',
    displayName: 'C',
    fileExtension: '.c',
    monacoLanguage: 'c',
    supportsExecution: false,
    defaultTemplate: `// C code
#include <stdio.h>

int main() {
    // Your code here
    printf("Hello, World!\\n");
    return 0;
}`,
    commentSyntax: {
      line: '//',
      blockStart: '/*',
      blockEnd: '*/',
    },
    features: {
      formatting: true,
      linting: false,
      testing: false,
      debugging: false,
    },
  },

  go: {
    id: 'go',
    name: 'Go',
    displayName: 'Go',
    fileExtension: '.go',
    monacoLanguage: 'go',
    supportsExecution: false,
    defaultTemplate: `// Go code
package main

import "fmt"

func main() {
    // Your code here
    fmt.Println("Hello, World!")
}`,
    commentSyntax: {
      line: '//',
      blockStart: '/*',
      blockEnd: '*/',
    },
    features: {
      formatting: true,
      linting: false,
      testing: false,
      debugging: false,
    },
  },

  rust: {
    id: 'rust',
    name: 'Rust',
    displayName: 'Rust',
    fileExtension: '.rs',
    monacoLanguage: 'rust',
    supportsExecution: false,
    defaultTemplate: `// Rust code
fn main() {
    // Your code here
    println!("Hello, World!");
}`,
    commentSyntax: {
      line: '//',
      blockStart: '/*',
      blockEnd: '*/',
    },
    features: {
      formatting: true,
      linting: false,
      testing: false,
      debugging: false,
    },
  },

  php: {
    id: 'php',
    name: 'PHP',
    displayName: 'PHP',
    fileExtension: '.php',
    monacoLanguage: 'php',
    supportsExecution: false,
    defaultTemplate: `<?php
// PHP code
function solution() {
    // Your code here
    echo "Hello, World!\\n";
}

solution();
?>`,
    commentSyntax: {
      line: '//',
      blockStart: '/*',
      blockEnd: '*/',
    },
    features: {
      formatting: true,
      linting: false,
      testing: false,
      debugging: false,
    },
  },

  ruby: {
    id: 'ruby',
    name: 'Ruby',
    displayName: 'Ruby',
    fileExtension: '.rb',
    monacoLanguage: 'ruby',
    supportsExecution: false,
    defaultTemplate: `# Ruby code
def solution
  # Your code here
  puts "Hello, World!"
end

solution`,
    commentSyntax: {
      line: '#',
      blockStart: '=begin',
      blockEnd: '=end',
    },
    features: {
      formatting: true,
      linting: false,
      testing: false,
      debugging: false,
    },
  },
};

// Get language configuration
export function getLanguageConfig(languageId: string): LanguageConfig | null {
  return LANGUAGE_CONFIGS[languageId] || null;
}

// Get all supported languages
export function getSupportedLanguages(): LanguageConfig[] {
  return Object.values(LANGUAGE_CONFIGS);
}

// Get executable languages
export function getExecutableLanguages(): LanguageConfig[] {
  return Object.values(LANGUAGE_CONFIGS).filter(lang => lang.supportsExecution);
}

// Get languages with specific features
export function getLanguagesWithFeature(feature: keyof LanguageConfig['features']): LanguageConfig[] {
  return Object.values(LANGUAGE_CONFIGS).filter(lang => lang.features[feature]);
}

// Check if language supports a feature
export function languageSupportsFeature(
  languageId: string, 
  feature: keyof LanguageConfig['features']
): boolean {
  const config = getLanguageConfig(languageId);
  return config ? config.features[feature] : false;
}

// Get file extension for language
export function getFileExtension(languageId: string): string {
  const config = getLanguageConfig(languageId);
  return config ? config.fileExtension : '.txt';
}

// Get Monaco language ID
export function getMonacoLanguage(languageId: string): string {
  const config = getLanguageConfig(languageId);
  return config ? config.monacoLanguage : 'plaintext';
}

// Get comment syntax for language
export function getCommentSyntax(languageId: string): LanguageConfig['commentSyntax'] | null {
  const config = getLanguageConfig(languageId);
  return config ? config.commentSyntax : null;
}
