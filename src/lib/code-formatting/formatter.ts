// Code formatting utilities for different languages

export interface FormatOptions {
  tabSize?: number;
  insertSpaces?: boolean;
  trimTrailingWhitespace?: boolean;
  insertFinalNewline?: boolean;
  semicolons?: boolean;
  singleQuote?: boolean;
  trailingComma?: 'none' | 'es5' | 'all';
}

export interface FormatResult {
  success: boolean;
  formattedCode?: string;
  error?: string;
}

// Default formatting options for different languages
const DEFAULT_OPTIONS: Record<string, FormatOptions> = {
  javascript: {
    tabSize: 2,
    insertSpaces: true,
    trimTrailingWhitespace: true,
    insertFinalNewline: true,
    semicolons: true,
    singleQuote: true,
    trailingComma: 'es5',
  },
  typescript: {
    tabSize: 2,
    insertSpaces: true,
    trimTrailingWhitespace: true,
    insertFinalNewline: true,
    semicolons: true,
    singleQuote: true,
    trailingComma: 'all',
  },
  python: {
    tabSize: 4,
    insertSpaces: true,
    trimTrailingWhitespace: true,
    insertFinalNewline: true,
  },
  java: {
    tabSize: 4,
    insertSpaces: true,
    trimTrailingWhitespace: true,
    insertFinalNewline: true,
    semicolons: true,
  },
  cpp: {
    tabSize: 2,
    insertSpaces: true,
    trimTrailingWhitespace: true,
    insertFinalNewline: true,
    semicolons: true,
  },
};

// Basic formatting functions for each language
class CodeFormatter {
  // Format JavaScript/TypeScript code
  static formatJavaScript(code: string, options: FormatOptions = {}): FormatResult {
    try {
      const opts = { ...DEFAULT_OPTIONS.javascript, ...options };
      let formatted = code;

      // Basic formatting rules
      formatted = this.normalizeIndentation(formatted, opts);
      formatted = this.normalizeLineEndings(formatted);
      formatted = this.trimTrailingWhitespace(formatted, opts);
      formatted = this.formatBraces(formatted);
      formatted = this.formatSpacing(formatted);
      
      if (opts.insertFinalNewline && !formatted.endsWith('\n')) {
        formatted += '\n';
      }

      return {
        success: true,
        formattedCode: formatted,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown formatting error',
      };
    }
  }

  // Format Python code
  static formatPython(code: string, options: FormatOptions = {}): FormatResult {
    try {
      const opts = { ...DEFAULT_OPTIONS.python, ...options };
      let formatted = code;

      // Python-specific formatting
      formatted = this.normalizeIndentation(formatted, opts);
      formatted = this.normalizeLineEndings(formatted);
      formatted = this.trimTrailingWhitespace(formatted, opts);
      formatted = this.formatPythonSpacing(formatted);
      
      if (opts.insertFinalNewline && !formatted.endsWith('\n')) {
        formatted += '\n';
      }

      return {
        success: true,
        formattedCode: formatted,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown formatting error',
      };
    }
  }

  // Format Java code
  static formatJava(code: string, options: FormatOptions = {}): FormatResult {
    try {
      const opts = { ...DEFAULT_OPTIONS.java, ...options };
      let formatted = code;

      formatted = this.normalizeIndentation(formatted, opts);
      formatted = this.normalizeLineEndings(formatted);
      formatted = this.trimTrailingWhitespace(formatted, opts);
      formatted = this.formatBraces(formatted);
      formatted = this.formatSpacing(formatted);
      
      if (opts.insertFinalNewline && !formatted.endsWith('\n')) {
        formatted += '\n';
      }

      return {
        success: true,
        formattedCode: formatted,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown formatting error',
      };
    }
  }

  // Normalize indentation
  private static normalizeIndentation(code: string, options: FormatOptions): string {
    const { tabSize = 2, insertSpaces = true } = options;
    const indentChar = insertSpaces ? ' '.repeat(tabSize) : '\t';
    
    return code.replace(/^[\t ]+/gm, (match) => {
      // Convert tabs to spaces or vice versa
      const spaces = match.replace(/\t/g, ' '.repeat(tabSize));
      const indentLevel = Math.floor(spaces.length / tabSize);
      return indentChar.repeat(indentLevel);
    });
  }

  // Normalize line endings
  private static normalizeLineEndings(code: string): string {
    return code.replace(/\r\n|\r/g, '\n');
  }

  // Trim trailing whitespace
  private static trimTrailingWhitespace(code: string, options: FormatOptions): string {
    if (!options.trimTrailingWhitespace) return code;
    return code.replace(/[ \t]+$/gm, '');
  }

  // Format braces for C-style languages
  private static formatBraces(code: string): string {
    // Add space before opening braces
    code = code.replace(/(\w)\{/g, '$1 {');
    
    // Ensure proper spacing around braces
    code = code.replace(/\{\s*\n/g, '{\n');
    code = code.replace(/\n\s*\}/g, '\n}');
    
    return code;
  }

  // Format general spacing
  private static formatSpacing(code: string): string {
    // Add space after commas
    code = code.replace(/,(?!\s)/g, ', ');
    
    // Add space around operators
    code = code.replace(/([^=!<>])=([^=])/g, '$1 = $2');
    code = code.replace(/([^=!<>])==([^=])/g, '$1 == $2');
    code = code.replace(/([^=!<>])!=([^=])/g, '$1 != $2');
    code = code.replace(/([^<>])<=([^=])/g, '$1 <= $2');
    code = code.replace(/([^<>])>=([^=])/g, '$1 >= $2');
    
    // Add space after keywords
    code = code.replace(/\b(if|for|while|switch|catch)\(/g, '$1 (');
    
    return code;
  }

  // Python-specific spacing
  private static formatPythonSpacing(code: string): string {
    // Add space after commas
    code = code.replace(/,(?!\s)/g, ', ');
    
    // Add space around operators (Python style)
    code = code.replace(/([^=!<>])=([^=])/g, '$1 = $2');
    code = code.replace(/([^=!<>])==([^=])/g, '$1 == $2');
    code = code.replace(/([^=!<>])!=([^=])/g, '$1 != $2');
    
    // Add space after keywords
    code = code.replace(/\b(if|for|while|elif|except)\(/g, '$1 (');
    
    // Format function definitions
    code = code.replace(/def\s+(\w+)\(/g, 'def $1(');
    
    return code;
  }
}

// Main formatting function
export async function formatCode(
  code: string, 
  language: string, 
  options: FormatOptions = {}
): Promise<FormatResult> {
  if (!code.trim()) {
    return { success: true, formattedCode: code };
  }

  switch (language.toLowerCase()) {
    case 'javascript':
    case 'js':
      return CodeFormatter.formatJavaScript(code, options);
    
    case 'typescript':
    case 'ts':
      return CodeFormatter.formatJavaScript(code, { ...DEFAULT_OPTIONS.typescript, ...options });
    
    case 'python':
    case 'py':
      return CodeFormatter.formatPython(code, options);
    
    case 'java':
      return CodeFormatter.formatJava(code, options);
    
    case 'cpp':
    case 'c++':
    case 'cxx':
      return CodeFormatter.formatJava(code, { ...DEFAULT_OPTIONS.cpp, ...options });
    
    default:
      // For unsupported languages, do basic formatting
      try {
        const opts = { tabSize: 2, insertSpaces: true, ...options };
        let formatted = CodeFormatter.normalizeIndentation(code, opts);
        formatted = CodeFormatter.normalizeLineEndings(formatted);
        formatted = CodeFormatter.trimTrailingWhitespace(formatted, opts);
        
        if (opts.insertFinalNewline && !formatted.endsWith('\n')) {
          formatted += '\n';
        }

        return {
          success: true,
          formattedCode: formatted,
        };
      } catch (error) {
        return {
          success: false,
          error: `Formatting not supported for ${language}`,
        };
      }
  }
}

// Get default options for a language
export function getDefaultFormatOptions(language: string): FormatOptions {
  return DEFAULT_OPTIONS[language.toLowerCase()] || DEFAULT_OPTIONS.javascript;
}

// Validate code syntax (basic)
export function validateSyntax(code: string, language: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  try {
    switch (language.toLowerCase()) {
      case 'javascript':
      case 'typescript':
        // Basic JS/TS validation
        if (code.includes('function') && !code.includes('{')) {
          errors.push('Function declaration missing opening brace');
        }
        break;
      
      case 'python':
        // Basic Python validation
        const lines = code.split('\n');
        let indentLevel = 0;
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i];
          if (line.trim().endsWith(':')) {
            // Should be followed by indented block
            if (i + 1 < lines.length) {
              const nextLine = lines[i + 1];
              if (nextLine.trim() && !nextLine.startsWith(' ') && !nextLine.startsWith('\t')) {
                errors.push(`Line ${i + 2}: Expected indented block after colon`);
              }
            }
          }
        }
        break;
    }
  } catch (error) {
    errors.push('Syntax validation failed');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}
