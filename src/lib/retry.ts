// Retry mechanism utilities for improved reliability

export interface RetryOptions {
  maxAttempts?: number;
  delay?: number;
  backoff?: 'linear' | 'exponential';
  maxDelay?: number;
  retryCondition?: (error: any) => boolean;
  onRetry?: (attempt: number, error: any) => void;
}

export interface RetryResult<T> {
  success: boolean;
  data?: T;
  error?: any;
  attempts: number;
}

// Default retry condition - retry on network errors and 5xx status codes
const defaultRetryCondition = (error: any): boolean => {
  // Network errors
  if (error.name === 'NetworkError' || error.message?.includes('fetch')) {
    return true;
  }

  // HTTP errors
  if (error.status >= 500 && error.status < 600) {
    return true;
  }

  // Timeout errors
  if (error.name === 'TimeoutError' || error.code === 'TIMEOUT') {
    return true;
  }

  // Appwrite specific errors that should be retried
  if (error.type === 'network_request_failed' || error.type === 'general_rate_limit_exceeded') {
    return true;
  }

  return false;
};

// Calculate delay based on backoff strategy
const calculateDelay = (attempt: number, baseDelay: number, backoff: 'linear' | 'exponential', maxDelay: number): number => {
  let delay: number;
  
  if (backoff === 'exponential') {
    delay = baseDelay * Math.pow(2, attempt - 1);
  } else {
    delay = baseDelay * attempt;
  }

  return Math.min(delay, maxDelay);
};

// Sleep utility
const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Generic retry function
export async function retry<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<RetryResult<T>> {
  const {
    maxAttempts = 3,
    delay = 1000,
    backoff = 'exponential',
    maxDelay = 10000,
    retryCondition = defaultRetryCondition,
    onRetry,
  } = options;

  let lastError: any;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      const result = await fn();
      return {
        success: true,
        data: result,
        attempts: attempt,
      };
    } catch (error) {
      lastError = error;
      
      // Don't retry if this is the last attempt
      if (attempt === maxAttempts) {
        break;
      }

      // Check if we should retry this error
      if (!retryCondition(error)) {
        break;
      }

      // Call retry callback
      onRetry?.(attempt, error);

      // Calculate and wait for delay
      const waitTime = calculateDelay(attempt, delay, backoff, maxDelay);
      await sleep(waitTime);
    }
  }

  return {
    success: false,
    error: lastError,
    attempts: maxAttempts,
  };
}

// Retry with exponential backoff and jitter
export async function retryWithJitter<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<RetryResult<T>> {
  return retry(fn, {
    ...options,
    delay: options.delay ? options.delay + Math.random() * 1000 : 1000 + Math.random() * 1000,
  });
}

// Specific retry functions for common operations

// Retry API calls
export async function retryApiCall<T>(
  apiCall: () => Promise<T>,
  options: Partial<RetryOptions> = {}
): Promise<T> {
  const result = await retry(apiCall, {
    maxAttempts: 3,
    delay: 1000,
    backoff: 'exponential',
    retryCondition: (error) => {
      // Retry on network errors, 5xx errors, and rate limits
      return defaultRetryCondition(error) || error.status === 429;
    },
    onRetry: (attempt, error) => {
      console.warn(`API call failed (attempt ${attempt}):`, error.message);
    },
    ...options,
  });

  if (!result.success) {
    throw result.error;
  }

  return result.data!;
}

// Retry file operations
export async function retryFileOperation<T>(
  operation: () => Promise<T>,
  options: Partial<RetryOptions> = {}
): Promise<T> {
  const result = await retry(operation, {
    maxAttempts: 2,
    delay: 500,
    backoff: 'linear',
    retryCondition: (error) => {
      // Retry on network errors and temporary failures
      return error.name === 'NetworkError' || 
             error.message?.includes('temporary') ||
             error.status >= 500;
    },
    onRetry: (attempt, error) => {
      console.warn(`File operation failed (attempt ${attempt}):`, error.message);
    },
    ...options,
  });

  if (!result.success) {
    throw result.error;
  }

  return result.data!;
}

// Retry code execution
export async function retryCodeExecution<T>(
  execution: () => Promise<T>,
  options: Partial<RetryOptions> = {}
): Promise<T> {
  const result = await retry(execution, {
    maxAttempts: 2,
    delay: 1000,
    backoff: 'linear',
    retryCondition: (error) => {
      // Only retry on specific execution errors, not syntax errors
      return error.message?.includes('timeout') ||
             error.message?.includes('network') ||
             error.name === 'NetworkError';
    },
    onRetry: (attempt, error) => {
      console.warn(`Code execution failed (attempt ${attempt}):`, error.message);
    },
    ...options,
  });

  if (!result.success) {
    throw result.error;
  }

  return result.data!;
}

// Circuit breaker pattern
export class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'closed' | 'open' | 'half-open' = 'closed';

  constructor(
    private threshold: number = 5,
    private timeout: number = 60000 // 1 minute
  ) {}

  async execute<T>(fn: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'half-open';
      } else {
        throw new Error('Circuit breaker is open');
      }
    }

    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess() {
    this.failures = 0;
    this.state = 'closed';
  }

  private onFailure() {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.threshold) {
      this.state = 'open';
    }
  }

  getState() {
    return {
      state: this.state,
      failures: this.failures,
      lastFailureTime: this.lastFailureTime,
    };
  }

  reset() {
    this.failures = 0;
    this.lastFailureTime = 0;
    this.state = 'closed';
  }
}

// Global circuit breaker instances
export const apiCircuitBreaker = new CircuitBreaker(5, 60000);
export const fileCircuitBreaker = new CircuitBreaker(3, 30000);
