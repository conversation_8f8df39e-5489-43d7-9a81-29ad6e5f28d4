// Performance monitoring and optimization utilities

export interface PerformanceMetrics {
  // Core Web Vitals
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  ttfb?: number; // Time to First Byte
  
  // Custom metrics
  editorLoadTime?: number;
  codeExecutionTime?: number;
  bundleSize?: number;
  memoryUsage?: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {};
  private observers: PerformanceObserver[] = [];

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeObservers();
    }
  }

  private initializeObservers() {
    // Observe Core Web Vitals
    this.observeWebVitals();
    
    // Observe resource loading
    this.observeResources();
    
    // Observe memory usage
    this.observeMemory();
  }

  private observeWebVitals() {
    try {
      // First Contentful Paint
      const fcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
        if (fcpEntry) {
          this.metrics.fcp = fcpEntry.startTime;
          console.log('FCP:', fcpEntry.startTime);
        }
      });
      fcpObserver.observe({ entryTypes: ['paint'] });
      this.observers.push(fcpObserver);

      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.lcp = lastEntry.startTime;
        console.log('LCP:', lastEntry.startTime);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(lcpObserver);

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          this.metrics.fid = entry.processingStart - entry.startTime;
          console.log('FID:', this.metrics.fid);
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.push(fidObserver);

      // Cumulative Layout Shift
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        this.metrics.cls = clsValue;
        console.log('CLS:', clsValue);
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(clsObserver);

    } catch (error) {
      console.warn('Performance observers not supported:', error);
    }
  }

  private observeResources() {
    try {
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          // Track Monaco Editor loading time
          if (entry.name.includes('monaco') || entry.name.includes('editor')) {
            this.metrics.editorLoadTime = entry.duration;
            console.log('Editor load time:', entry.duration);
          }
        });
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);
    } catch (error) {
      console.warn('Resource observer not supported:', error);
    }
  }

  private observeMemory() {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        this.metrics.memoryUsage = memory.usedJSHeapSize;
        
        // Log memory usage if it's getting high
        if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.8) {
          console.warn('High memory usage detected:', {
            used: memory.usedJSHeapSize,
            total: memory.totalJSHeapSize,
            limit: memory.jsHeapSizeLimit,
          });
        }
      }, 30000); // Check every 30 seconds
    }
  }

  // Measure custom performance metrics
  measureCodeExecution<T>(fn: () => T, label: string): T {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    const duration = end - start;
    
    console.log(`${label} execution time:`, duration);
    
    if (label.includes('code')) {
      this.metrics.codeExecutionTime = duration;
    }
    
    return result;
  }

  // Measure async operations
  async measureAsync<T>(fn: () => Promise<T>, label: string): Promise<T> {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();
    const duration = end - start;
    
    console.log(`${label} async execution time:`, duration);
    return result;
  }

  // Get current metrics
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  // Get performance score (0-100)
  getPerformanceScore(): number {
    const { fcp, lcp, fid, cls } = this.metrics;
    let score = 100;

    // Deduct points based on Core Web Vitals thresholds
    if (fcp && fcp > 1800) score -= 20; // FCP should be < 1.8s
    if (lcp && lcp > 2500) score -= 30; // LCP should be < 2.5s
    if (fid && fid > 100) score -= 25; // FID should be < 100ms
    if (cls && cls > 0.1) score -= 25; // CLS should be < 0.1

    return Math.max(0, score);
  }

  // Clean up observers
  disconnect() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Utility functions
export function measureRender(componentName: string) {
  return function <T extends React.ComponentType<any>>(Component: T): T {
    const MeasuredComponent = (props: any) => {
      const start = performance.now();
      
      React.useEffect(() => {
        const end = performance.now();
        console.log(`${componentName} render time:`, end - start);
      });

      return React.createElement(Component, props);
    };

    MeasuredComponent.displayName = `Measured(${componentName})`;
    return MeasuredComponent as T;
  };
}

// Bundle size analyzer
export function analyzeBundleSize() {
  if (typeof window === 'undefined') return;

  const scripts = Array.from(document.querySelectorAll('script[src]'));
  const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
  
  let totalSize = 0;
  const resources: { name: string; size: number; type: string }[] = [];

  // Analyze loaded resources
  performance.getEntriesByType('resource').forEach((entry: any) => {
    if (entry.transferSize) {
      totalSize += entry.transferSize;
      resources.push({
        name: entry.name,
        size: entry.transferSize,
        type: entry.initiatorType,
      });
    }
  });

  console.log('Bundle Analysis:', {
    totalSize: `${(totalSize / 1024).toFixed(2)} KB`,
    resources: resources.sort((a, b) => b.size - a.size).slice(0, 10), // Top 10 largest
  });

  return { totalSize, resources };
}

// Memory leak detector
export function detectMemoryLeaks() {
  if (!('memory' in performance)) {
    console.warn('Memory API not available');
    return;
  }

  const memory = (performance as any).memory;
  const baseline = memory.usedJSHeapSize;
  
  setTimeout(() => {
    const current = memory.usedJSHeapSize;
    const increase = current - baseline;
    
    if (increase > 10 * 1024 * 1024) { // 10MB increase
      console.warn('Potential memory leak detected:', {
        baseline: `${(baseline / 1024 / 1024).toFixed(2)} MB`,
        current: `${(current / 1024 / 1024).toFixed(2)} MB`,
        increase: `${(increase / 1024 / 1024).toFixed(2)} MB`,
      });
    }
  }, 60000); // Check after 1 minute
}

// Preload critical resources
export function preloadResource(href: string, as: string, crossorigin?: string) {
  if (typeof document === 'undefined') return;

  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = as;
  if (crossorigin) link.crossOrigin = crossorigin;
  
  document.head.appendChild(link);
}

// Lazy load images
export function lazyLoadImages() {
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          img.src = img.dataset.src || '';
          img.classList.remove('lazy');
          imageObserver.unobserve(img);
        }
      });
    });

    document.querySelectorAll('img[data-src]').forEach((img) => {
      imageObserver.observe(img);
    });
  }
}
