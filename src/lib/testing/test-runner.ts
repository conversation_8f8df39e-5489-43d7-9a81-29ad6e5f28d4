// Test runner system for code execution and validation

export interface TestCase {
  id: string;
  name: string;
  description?: string;
  input: any[];
  expectedOutput: any;
  timeout?: number;
}

export interface TestResult {
  testCase: TestCase;
  passed: boolean;
  actualOutput: any;
  executionTime: number;
  error?: string;
}

export interface TestSuite {
  name: string;
  tests: TestCase[];
  setup?: string;
  teardown?: string;
}

export interface TestRunResult {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  results: TestResult[];
  totalTime: number;
  coverage?: number;
}

// Test runner for JavaScript/TypeScript
class JavaScriptTestRunner {
  static async runTests(code: string, testSuite: TestSuite): Promise<TestRunResult> {
    const startTime = performance.now();
    const results: TestResult[] = [];

    // Prepare the execution environment
    const testEnvironment = this.createTestEnvironment(code, testSuite.setup);

    for (const testCase of testSuite.tests) {
      const testStartTime = performance.now();
      
      try {
        const result = await this.runSingleTest(testEnvironment, testCase);
        const testEndTime = performance.now();
        
        results.push({
          testCase,
          passed: result.passed,
          actualOutput: result.actualOutput,
          executionTime: testEndTime - testStartTime,
          error: result.error,
        });
      } catch (error) {
        const testEndTime = performance.now();
        results.push({
          testCase,
          passed: false,
          actualOutput: null,
          executionTime: testEndTime - testStartTime,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    const endTime = performance.now();
    const passedTests = results.filter(r => r.passed).length;

    return {
      totalTests: testSuite.tests.length,
      passedTests,
      failedTests: testSuite.tests.length - passedTests,
      results,
      totalTime: endTime - startTime,
    };
  }

  private static createTestEnvironment(code: string, setup?: string): string {
    return `
      ${setup || ''}
      
      ${code}
      
      // Test execution function
      function executeTest(functionName, args) {
        try {
          if (typeof window[functionName] === 'function') {
            return window[functionName](...args);
          } else if (typeof eval(functionName) === 'function') {
            return eval(functionName)(...args);
          } else {
            throw new Error('Function not found: ' + functionName);
          }
        } catch (error) {
          throw error;
        }
      }
    `;
  }

  private static async runSingleTest(
    environment: string, 
    testCase: TestCase
  ): Promise<{ passed: boolean; actualOutput: any; error?: string }> {
    try {
      // Create a sandboxed execution context
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      document.body.appendChild(iframe);

      const iframeWindow = iframe.contentWindow;
      if (!iframeWindow) {
        throw new Error('Failed to create test environment');
      }

      // Execute the code in the iframe
      const script = iframeWindow.document.createElement('script');
      script.textContent = environment;
      iframeWindow.document.head.appendChild(script);

      // Extract function name from test case (assuming first parameter is function name)
      const functionName = testCase.input[0];
      const args = testCase.input.slice(1);

      // Execute the test
      const actualOutput = (iframeWindow as any).executeTest(functionName, args);

      // Clean up
      document.body.removeChild(iframe);

      // Compare results
      const passed = this.compareResults(actualOutput, testCase.expectedOutput);

      return {
        passed,
        actualOutput,
      };
    } catch (error) {
      return {
        passed: false,
        actualOutput: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private static compareResults(actual: any, expected: any): boolean {
    // Deep equality check
    if (actual === expected) return true;
    
    if (Array.isArray(actual) && Array.isArray(expected)) {
      if (actual.length !== expected.length) return false;
      return actual.every((item, index) => this.compareResults(item, expected[index]));
    }
    
    if (typeof actual === 'object' && typeof expected === 'object' && actual !== null && expected !== null) {
      const actualKeys = Object.keys(actual);
      const expectedKeys = Object.keys(expected);
      
      if (actualKeys.length !== expectedKeys.length) return false;
      
      return actualKeys.every(key => 
        expectedKeys.includes(key) && this.compareResults(actual[key], expected[key])
      );
    }
    
    return false;
  }
}

// Test runner for Python (simplified)
class PythonTestRunner {
  static async runTests(code: string, testSuite: TestSuite): Promise<TestRunResult> {
    const startTime = performance.now();
    const results: TestResult[] = [];

    for (const testCase of testSuite.tests) {
      const testStartTime = performance.now();
      
      try {
        // For Python, we'll need to use Pyodide or similar
        // This is a simplified implementation
        const result = await this.runPythonTest(code, testCase);
        const testEndTime = performance.now();
        
        results.push({
          testCase,
          passed: result.passed,
          actualOutput: result.actualOutput,
          executionTime: testEndTime - testStartTime,
          error: result.error,
        });
      } catch (error) {
        const testEndTime = performance.now();
        results.push({
          testCase,
          passed: false,
          actualOutput: null,
          executionTime: testEndTime - testStartTime,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    const endTime = performance.now();
    const passedTests = results.filter(r => r.passed).length;

    return {
      totalTests: testSuite.tests.length,
      passedTests,
      failedTests: testSuite.tests.length - passedTests,
      results,
      totalTime: endTime - startTime,
    };
  }

  private static async runPythonTest(
    code: string, 
    testCase: TestCase
  ): Promise<{ passed: boolean; actualOutput: any; error?: string }> {
    // This would require Pyodide integration
    // For now, return a placeholder
    return {
      passed: false,
      actualOutput: null,
      error: 'Python test execution not implemented yet',
    };
  }
}

// Main test runner function
export async function runTests(
  code: string, 
  language: string, 
  testSuite: TestSuite
): Promise<TestRunResult> {
  switch (language.toLowerCase()) {
    case 'javascript':
    case 'js':
    case 'typescript':
    case 'ts':
      return JavaScriptTestRunner.runTests(code, testSuite);
    
    case 'python':
    case 'py':
      return PythonTestRunner.runTests(code, testSuite);
    
    default:
      throw new Error(`Test runner not implemented for ${language}`);
  }
}

// Generate test cases from function signature
export function generateTestCases(functionName: string, language: string): TestCase[] {
  const commonTestCases: TestCase[] = [];

  // Generate basic test cases based on common patterns
  switch (language.toLowerCase()) {
    case 'javascript':
    case 'typescript':
      commonTestCases.push(
        {
          id: 'test-1',
          name: 'Basic functionality',
          input: [functionName, 1, 2],
          expectedOutput: 3,
        },
        {
          id: 'test-2',
          name: 'Edge case - empty input',
          input: [functionName],
          expectedOutput: null,
        },
        {
          id: 'test-3',
          name: 'Edge case - negative numbers',
          input: [functionName, -1, -2],
          expectedOutput: -3,
        }
      );
      break;
    
    case 'python':
      commonTestCases.push(
        {
          id: 'test-1',
          name: 'Basic functionality',
          input: [functionName, [1, 2, 3]],
          expectedOutput: 6,
        },
        {
          id: 'test-2',
          name: 'Edge case - empty list',
          input: [functionName, []],
          expectedOutput: 0,
        }
      );
      break;
  }

  return commonTestCases;
}

// Create test suite from problem description
export function createTestSuite(
  problemName: string,
  testCases: TestCase[],
  setup?: string
): TestSuite {
  return {
    name: problemName,
    tests: testCases,
    setup,
  };
}

// Validate test case format
export function validateTestCase(testCase: TestCase): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!testCase.id) errors.push('Test case must have an ID');
  if (!testCase.name) errors.push('Test case must have a name');
  if (!Array.isArray(testCase.input)) errors.push('Test case input must be an array');
  if (testCase.expectedOutput === undefined) errors.push('Test case must have expected output');

  return {
    valid: errors.length === 0,
    errors,
  };
}

// Format test results for display
export function formatTestResults(results: TestRunResult): string {
  const { totalTests, passedTests, failedTests, totalTime } = results;
  
  let output = `\n=== Test Results ===\n`;
  output += `Total: ${totalTests}, Passed: ${passedTests}, Failed: ${failedTests}\n`;
  output += `Execution time: ${totalTime.toFixed(2)}ms\n\n`;

  results.results.forEach((result, index) => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    output += `${index + 1}. ${status} - ${result.testCase.name}\n`;
    
    if (!result.passed) {
      output += `   Expected: ${JSON.stringify(result.testCase.expectedOutput)}\n`;
      output += `   Actual: ${JSON.stringify(result.actualOutput)}\n`;
      if (result.error) {
        output += `   Error: ${result.error}\n`;
      }
    }
    
    output += `   Time: ${result.executionTime.toFixed(2)}ms\n\n`;
  });

  return output;
}
