// Basic linting system for code quality checks

export interface LintRule {
  id: string;
  name: string;
  description: string;
  severity: 'error' | 'warning' | 'info';
  category: 'syntax' | 'style' | 'performance' | 'security' | 'best-practice';
}

export interface LintIssue {
  rule: LintRule;
  message: string;
  line: number;
  column: number;
  endLine?: number;
  endColumn?: number;
  suggestion?: string;
}

export interface LintResult {
  issues: LintIssue[];
  errorCount: number;
  warningCount: number;
  infoCount: number;
}

// Common lint rules
const COMMON_RULES: Record<string, LintRule> = {
  'no-unused-vars': {
    id: 'no-unused-vars',
    name: 'No Unused Variables',
    description: 'Variables should be used after declaration',
    severity: 'warning',
    category: 'best-practice',
  },
  'no-console': {
    id: 'no-console',
    name: 'No Console Statements',
    description: 'Avoid console statements in production code',
    severity: 'info',
    category: 'best-practice',
  },
  'missing-semicolon': {
    id: 'missing-semicolon',
    name: 'Missing Semicolon',
    description: 'Statements should end with semicolons',
    severity: 'warning',
    category: 'style',
  },
  'long-line': {
    id: 'long-line',
    name: 'Line Too Long',
    description: 'Lines should not exceed recommended length',
    severity: 'info',
    category: 'style',
  },
  'trailing-whitespace': {
    id: 'trailing-whitespace',
    name: 'Trailing Whitespace',
    description: 'Lines should not have trailing whitespace',
    severity: 'info',
    category: 'style',
  },
  'inconsistent-indentation': {
    id: 'inconsistent-indentation',
    name: 'Inconsistent Indentation',
    description: 'Indentation should be consistent throughout the file',
    severity: 'warning',
    category: 'style',
  },
};

class CodeLinter {
  // Lint JavaScript/TypeScript code
  static lintJavaScript(code: string): LintResult {
    const issues: LintIssue[] = [];
    const lines = code.split('\n');

    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      const trimmedLine = line.trim();

      // Check for trailing whitespace
      if (line.endsWith(' ') || line.endsWith('\t')) {
        issues.push({
          rule: COMMON_RULES['trailing-whitespace'],
          message: 'Line has trailing whitespace',
          line: lineNumber,
          column: line.length,
          suggestion: 'Remove trailing whitespace',
        });
      }

      // Check for long lines (>120 characters)
      if (line.length > 120) {
        issues.push({
          rule: COMMON_RULES['long-line'],
          message: `Line is ${line.length} characters long (max 120)`,
          line: lineNumber,
          column: 121,
          suggestion: 'Break long line into multiple lines',
        });
      }

      // Check for console statements
      if (trimmedLine.includes('console.')) {
        const column = line.indexOf('console.') + 1;
        issues.push({
          rule: COMMON_RULES['no-console'],
          message: 'Unexpected console statement',
          line: lineNumber,
          column,
          suggestion: 'Remove console statement or use proper logging',
        });
      }

      // Check for missing semicolons (basic check)
      if (trimmedLine && 
          !trimmedLine.endsWith(';') && 
          !trimmedLine.endsWith('{') && 
          !trimmedLine.endsWith('}') &&
          !trimmedLine.startsWith('//') &&
          !trimmedLine.startsWith('/*') &&
          !trimmedLine.includes('if ') &&
          !trimmedLine.includes('for ') &&
          !trimmedLine.includes('while ') &&
          !trimmedLine.includes('function ') &&
          !trimmedLine.includes('class ') &&
          trimmedLine.includes('=')) {
        issues.push({
          rule: COMMON_RULES['missing-semicolon'],
          message: 'Missing semicolon',
          line: lineNumber,
          column: line.length + 1,
          suggestion: 'Add semicolon at end of statement',
        });
      }
    });

    // Check for unused variables (basic pattern matching)
    this.checkUnusedVariables(code, issues);

    // Check indentation consistency
    this.checkIndentationConsistency(lines, issues);

    return this.createLintResult(issues);
  }

  // Lint Python code
  static lintPython(code: string): LintResult {
    const issues: LintIssue[] = [];
    const lines = code.split('\n');

    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      const trimmedLine = line.trim();

      // Check for trailing whitespace
      if (line.endsWith(' ') || line.endsWith('\t')) {
        issues.push({
          rule: COMMON_RULES['trailing-whitespace'],
          message: 'Line has trailing whitespace',
          line: lineNumber,
          column: line.length,
          suggestion: 'Remove trailing whitespace',
        });
      }

      // Check for long lines (>79 characters for Python)
      if (line.length > 79) {
        issues.push({
          rule: COMMON_RULES['long-line'],
          message: `Line is ${line.length} characters long (max 79 for Python)`,
          line: lineNumber,
          column: 80,
          suggestion: 'Break long line into multiple lines',
        });
      }

      // Check for print statements
      if (trimmedLine.includes('print(')) {
        const column = line.indexOf('print(') + 1;
        issues.push({
          rule: COMMON_RULES['no-console'],
          message: 'Consider using logging instead of print',
          line: lineNumber,
          column,
          suggestion: 'Use logging module for better control',
        });
      }
    });

    // Check Python-specific indentation (4 spaces)
    this.checkPythonIndentation(lines, issues);

    return this.createLintResult(issues);
  }

  // Check for unused variables (basic implementation)
  private static checkUnusedVariables(code: string, issues: LintIssue[]) {
    const variableDeclarations = code.match(/(?:var|let|const)\s+(\w+)/g);
    if (!variableDeclarations) return;

    variableDeclarations.forEach((declaration) => {
      const match = declaration.match(/(?:var|let|const)\s+(\w+)/);
      if (!match) return;

      const variableName = match[1];
      const declarationIndex = code.indexOf(declaration);
      const restOfCode = code.substring(declarationIndex + declaration.length);

      // Simple check: if variable name doesn't appear again, it might be unused
      if (!restOfCode.includes(variableName)) {
        const lines = code.substring(0, declarationIndex).split('\n');
        const lineNumber = lines.length;
        
        issues.push({
          rule: COMMON_RULES['no-unused-vars'],
          message: `Variable '${variableName}' is declared but never used`,
          line: lineNumber,
          column: lines[lines.length - 1].length + 1,
          suggestion: `Remove unused variable '${variableName}'`,
        });
      }
    });
  }

  // Check indentation consistency
  private static checkIndentationConsistency(lines: string[], issues: LintIssue[]) {
    let hasSpaces = false;
    let hasTabs = false;
    let spaceCount = 0;

    lines.forEach((line, index) => {
      if (line.trim() === '') return; // Skip empty lines

      const leadingWhitespace = line.match(/^[\s]*/)?.[0] || '';
      if (leadingWhitespace.includes(' ')) hasSpaces = true;
      if (leadingWhitespace.includes('\t')) hasTabs = true;

      // Check for consistent space count
      if (hasSpaces && leadingWhitespace.length > 0) {
        const spaces = leadingWhitespace.replace(/\t/g, '').length;
        if (spaceCount === 0 && spaces > 0) {
          spaceCount = spaces;
        } else if (spaceCount > 0 && spaces > 0 && spaces % spaceCount !== 0) {
          issues.push({
            rule: COMMON_RULES['inconsistent-indentation'],
            message: `Inconsistent indentation (expected multiple of ${spaceCount} spaces)`,
            line: index + 1,
            column: 1,
            suggestion: `Use ${spaceCount} spaces for each indentation level`,
          });
        }
      }
    });

    // Mixed tabs and spaces
    if (hasSpaces && hasTabs) {
      issues.push({
        rule: COMMON_RULES['inconsistent-indentation'],
        message: 'Mixed tabs and spaces for indentation',
        line: 1,
        column: 1,
        suggestion: 'Use either tabs or spaces consistently, not both',
      });
    }
  }

  // Check Python-specific indentation
  private static checkPythonIndentation(lines: string[], issues: LintIssue[]) {
    lines.forEach((line, index) => {
      if (line.trim() === '') return;

      const leadingSpaces = line.match(/^ */)?.[0]?.length || 0;
      if (leadingSpaces > 0 && leadingSpaces % 4 !== 0) {
        issues.push({
          rule: COMMON_RULES['inconsistent-indentation'],
          message: 'Python indentation should be 4 spaces per level',
          line: index + 1,
          column: 1,
          suggestion: 'Use 4 spaces for each indentation level',
        });
      }
    });
  }

  // Create lint result summary
  private static createLintResult(issues: LintIssue[]): LintResult {
    const errorCount = issues.filter(issue => issue.rule.severity === 'error').length;
    const warningCount = issues.filter(issue => issue.rule.severity === 'warning').length;
    const infoCount = issues.filter(issue => issue.rule.severity === 'info').length;

    return {
      issues,
      errorCount,
      warningCount,
      infoCount,
    };
  }
}

// Main linting function
export async function lintCode(code: string, language: string): Promise<LintResult> {
  if (!code.trim()) {
    return { issues: [], errorCount: 0, warningCount: 0, infoCount: 0 };
  }

  switch (language.toLowerCase()) {
    case 'javascript':
    case 'js':
    case 'typescript':
    case 'ts':
      return CodeLinter.lintJavaScript(code);
    
    case 'python':
    case 'py':
      return CodeLinter.lintPython(code);
    
    default:
      // Basic linting for unsupported languages
      const issues: LintIssue[] = [];
      const lines = code.split('\n');
      
      lines.forEach((line, index) => {
        if (line.endsWith(' ') || line.endsWith('\t')) {
          issues.push({
            rule: COMMON_RULES['trailing-whitespace'],
            message: 'Line has trailing whitespace',
            line: index + 1,
            column: line.length,
            suggestion: 'Remove trailing whitespace',
          });
        }
      });

      return CodeLinter.createLintResult(issues);
  }
}

// Get available rules for a language
export function getAvailableRules(language: string): LintRule[] {
  return Object.values(COMMON_RULES);
}

// Fix auto-fixable issues
export function autoFix(code: string, issues: LintIssue[]): string {
  let fixedCode = code;
  const lines = fixedCode.split('\n');

  // Fix trailing whitespace
  issues.forEach(issue => {
    if (issue.rule.id === 'trailing-whitespace') {
      const lineIndex = issue.line - 1;
      if (lineIndex < lines.length) {
        lines[lineIndex] = lines[lineIndex].trimEnd();
      }
    }
  });

  return lines.join('\n');
}
