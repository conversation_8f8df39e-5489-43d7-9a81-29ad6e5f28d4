'use client';

import { useEffect, useState } from 'react';
import { registerServiceWorker, addNetworkListeners } from '@/lib/service-worker';
import { performanceMonitor, detectMemoryLeaks } from '@/lib/performance';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Wifi, WifiOff, RefreshCw, X } from 'lucide-react';

interface PerformanceProviderProps {
  children: React.ReactNode;
}

export function PerformanceProvider({ children }: PerformanceProviderProps) {
  const [isOnline, setIsOnline] = useState(true);
  const [showUpdateBanner, setShowUpdateBanner] = useState(false);
  const [swRegistration, setSwRegistration] = useState<ServiceWorkerRegistration | null>(null);

  useEffect(() => {
    // Initialize performance monitoring
    detectMemoryLeaks();

    // Register service worker
    registerServiceWorker({
      onUpdate: (registration) => {
        setSwRegistration(registration);
        setShowUpdateBanner(true);
      },
      onSuccess: (registration) => {
        console.log('Service Worker registered successfully');
        setSwRegistration(registration);
      },
      onError: (error) => {
        console.error('Service Worker registration failed:', error);
      },
    });

    // Set up network listeners
    const cleanup = addNetworkListeners(
      () => setIsOnline(true),
      () => setIsOnline(false)
    );

    // Initial online status
    setIsOnline(navigator.onLine);

    return cleanup;
  }, []);

  const handleUpdate = () => {
    if (swRegistration?.waiting) {
      swRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
      window.location.reload();
    }
  };

  return (
    <>
      {children}
      
      {/* Network Status Indicator */}
      <div className="fixed bottom-4 left-4 z-50">
        <Badge
          variant={isOnline ? 'default' : 'destructive'}
          className="flex items-center gap-2 px-3 py-2"
        >
          {isOnline ? (
            <>
              <Wifi className="h-3 w-3" />
              Online
            </>
          ) : (
            <>
              <WifiOff className="h-3 w-3" />
              Offline
            </>
          )}
        </Badge>
      </div>

      {/* Update Banner */}
      {showUpdateBanner && (
        <div className="fixed top-0 left-0 right-0 z-50 bg-primary text-primary-foreground p-3">
          <div className="flex items-center justify-between max-w-7xl mx-auto">
            <div className="flex items-center gap-3">
              <RefreshCw className="h-4 w-4" />
              <span className="text-sm font-medium">
                A new version is available!
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="secondary"
                onClick={handleUpdate}
                className="text-xs"
              >
                Update Now
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowUpdateBanner(false)}
                className="text-xs p-1"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
