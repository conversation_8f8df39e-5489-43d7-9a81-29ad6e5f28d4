'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Menu,
  Code,
  FolderOpen,
  Terminal,
  Settings,
  X,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MobileResponsiveLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  fileTree?: React.ReactNode;
  terminal?: React.ReactNode;
  className?: string;
}

type MobileView = 'editor' | 'files' | 'terminal' | 'settings';

export function MobileResponsiveLayout({
  children,
  sidebar,
  fileTree,
  terminal,
  className,
}: MobileResponsiveLayoutProps) {
  const [isMobile, setIsMobile] = useState(false);
  const [currentView, setCurrentView] = useState<MobileView>('editor');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Detect mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Mobile layout
  if (isMobile) {
    return (
      <div className={cn('flex flex-col h-screen bg-background', className)}>
        {/* Mobile header */}
        <div className="flex items-center justify-between px-4 py-3 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex items-center gap-2">
            <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="p-2">
                  <Menu className="h-4 w-4" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-80 p-0">
                <div className="flex flex-col h-full">
                  <div className="flex items-center justify-between p-4 border-b">
                    <h2 className="text-lg font-semibold">Navigation</h2>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSidebarOpen(false)}
                      className="p-2"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex-1 overflow-auto">
                    {sidebar}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
            <h1 className="text-lg font-semibold">Codeable</h1>
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              className="p-2"
              onClick={() => setCurrentView('settings')}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Mobile content */}
        <div className="flex-1 flex flex-col min-h-0">
          {currentView === 'editor' && (
            <div className="flex-1 flex flex-col min-h-0">
              {children}
            </div>
          )}

          {currentView === 'files' && (
            <div className="flex-1 flex flex-col min-h-0 p-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">Files</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentView('editor')}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Back
                </Button>
              </div>
              <div className="flex-1 overflow-auto">
                {fileTree}
              </div>
            </div>
          )}

          {currentView === 'terminal' && (
            <div className="flex-1 flex flex-col min-h-0 p-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">Terminal</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentView('editor')}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Back
                </Button>
              </div>
              <div className="flex-1 overflow-auto">
                {terminal}
              </div>
            </div>
          )}

          {currentView === 'settings' && (
            <div className="flex-1 flex flex-col min-h-0 p-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">Settings</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentView('editor')}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Back
                </Button>
              </div>
              <div className="space-y-4">
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Editor Settings</h3>
                  <p className="text-sm text-muted-foreground">
                    Configure your editor preferences
                  </p>
                </div>
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Theme</h3>
                  <p className="text-sm text-muted-foreground">
                    Choose your preferred theme
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Mobile bottom navigation */}
        <div className="flex items-center justify-around py-2 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <Button
            variant={currentView === 'editor' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setCurrentView('editor')}
            className="flex-1 flex flex-col items-center gap-1 h-auto py-2"
          >
            <Code className="h-4 w-4" />
            <span className="text-xs">Editor</span>
          </Button>
          <Button
            variant={currentView === 'files' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setCurrentView('files')}
            className="flex-1 flex flex-col items-center gap-1 h-auto py-2"
          >
            <FolderOpen className="h-4 w-4" />
            <span className="text-xs">Files</span>
          </Button>
          <Button
            variant={currentView === 'terminal' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setCurrentView('terminal')}
            className="flex-1 flex flex-col items-center gap-1 h-auto py-2"
          >
            <Terminal className="h-4 w-4" />
            <span className="text-xs">Terminal</span>
          </Button>
        </div>
      </div>
    );
  }

  // Desktop layout (unchanged)
  return (
    <div className={cn('flex h-screen bg-background', className)}>
      {sidebar && (
        <div className="w-64 border-r bg-muted/30 flex flex-col">
          {sidebar}
        </div>
      )}
      <div className="flex-1 flex flex-col min-h-0">
        {children}
      </div>
    </div>
  );
}
