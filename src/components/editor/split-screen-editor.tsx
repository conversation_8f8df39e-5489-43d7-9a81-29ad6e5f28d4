'use client';

import { useState, useCallback } from 'react';
import { CodeEditor } from './code-editor';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  SplitSquareHorizontal,
  SplitSquareVertical,
  Maximize2,
  X,
  MoreHorizontal,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { File } from '@/types';

interface EditorPane {
  id: string;
  file: File;
  content: string;
  isDirty: boolean;
}

interface SplitScreenEditorProps {
  files: File[];
  activeFileId?: string;
  onFileChange?: (fileId: string, content: string) => void;
  onFileSave?: (fileId: string) => void;
  onFileClose?: (fileId: string) => void;
  onActiveFileChange?: (fileId: string) => void;
  className?: string;
}

type SplitMode = 'single' | 'horizontal' | 'vertical';

export function SplitScreenEditor({
  files,
  activeFileId,
  onFileChange,
  onFileSave,
  onFileClose,
  onActiveFileChange,
  className,
}: SplitScreenEditorProps) {
  const [splitMode, setSplitMode] = useState<SplitMode>('single');
  const [panes, setPanes] = useState<EditorPane[]>([]);
  const [activePaneId, setActivePaneId] = useState<string>('');

  // Initialize panes when files change
  const initializePanes = useCallback(() => {
    if (files.length === 0) {
      setPanes([]);
      return;
    }

    const activeFile = files.find(f => f.$id === activeFileId) || files[0];
    const newPane: EditorPane = {
      id: `pane-${activeFile.$id}`,
      file: activeFile,
      content: activeFile.content,
      isDirty: false,
    };

    setPanes([newPane]);
    setActivePaneId(newPane.id);
  }, [files, activeFileId]);

  // Add a new pane for split view
  const addPane = (file: File) => {
    const newPane: EditorPane = {
      id: `pane-${file.$id}-${Date.now()}`,
      file,
      content: file.content,
      isDirty: false,
    };

    setPanes(prev => [...prev, newPane]);
    setActivePaneId(newPane.id);
  };

  // Remove a pane
  const removePane = (paneId: string) => {
    setPanes(prev => {
      const filtered = prev.filter(p => p.id !== paneId);
      if (filtered.length === 0) {
        setSplitMode('single');
      } else if (filtered.length === 1) {
        setSplitMode('single');
      }
      return filtered;
    });

    // Set active pane to the first remaining pane
    setPanes(prev => {
      if (prev.length > 0 && activePaneId === paneId) {
        setActivePaneId(prev[0].id);
      }
      return prev;
    });
  };

  // Handle content change in a pane
  const handlePaneContentChange = (paneId: string, content: string) => {
    setPanes(prev => prev.map(pane => 
      pane.id === paneId 
        ? { ...pane, content, isDirty: content !== pane.file.content }
        : pane
    ));

    const pane = panes.find(p => p.id === paneId);
    if (pane) {
      onFileChange?.(pane.file.$id, content);
    }
  };

  // Handle save in a pane
  const handlePaneSave = (paneId: string) => {
    const pane = panes.find(p => p.id === paneId);
    if (pane) {
      onFileSave?.(pane.file.$id);
      setPanes(prev => prev.map(p => 
        p.id === paneId ? { ...p, isDirty: false } : p
      ));
    }
  };

  // Toggle split modes
  const toggleHorizontalSplit = () => {
    if (splitMode === 'horizontal') {
      setSplitMode('single');
      setPanes(prev => prev.slice(0, 1));
    } else {
      setSplitMode('horizontal');
      if (panes.length === 1 && files.length > 1) {
        const nextFile = files.find(f => f.$id !== panes[0].file.$id) || files[1];
        if (nextFile) addPane(nextFile);
      }
    }
  };

  const toggleVerticalSplit = () => {
    if (splitMode === 'vertical') {
      setSplitMode('single');
      setPanes(prev => prev.slice(0, 1));
    } else {
      setSplitMode('vertical');
      if (panes.length === 1 && files.length > 1) {
        const nextFile = files.find(f => f.$id !== panes[0].file.$id) || files[1];
        if (nextFile) addPane(nextFile);
      }
    }
  };

  // Initialize panes when component mounts or files change
  React.useEffect(() => {
    if (panes.length === 0) {
      initializePanes();
    }
  }, [initializePanes, panes.length]);

  if (panes.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center text-muted-foreground">
        No files open
      </div>
    );
  }

  const renderPane = (pane: EditorPane, index: number) => (
    <div key={pane.id} className="flex flex-col min-h-0 flex-1">
      {/* Pane header */}
      <div className="flex items-center justify-between px-3 py-2 border-b bg-muted/30">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">{pane.file.name}</span>
            {pane.isDirty && (
              <div className="w-2 h-2 bg-orange-500 rounded-full" />
            )}
          </div>
          <Badge variant="outline" className="text-xs">
            {pane.file.language}
          </Badge>
        </div>
        <div className="flex items-center gap-1">
          {panes.length > 1 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => removePane(pane.id)}
              className="h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>

      {/* Editor */}
      <div className="flex-1 min-h-0">
        <CodeEditor
          value={pane.content}
          language={pane.file.language}
          onChange={(content) => handlePaneContentChange(pane.id, content)}
          onSave={() => handlePaneSave(pane.id)}
          className="h-full"
        />
      </div>
    </div>
  );

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Toolbar */}
      <div className="flex items-center justify-between px-3 py-2 border-b bg-background">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Editor</span>
          {panes.length > 1 && (
            <Badge variant="secondary" className="text-xs">
              {panes.length} panes
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant={splitMode === 'horizontal' ? 'default' : 'ghost'}
            size="sm"
            onClick={toggleHorizontalSplit}
            className="h-7 px-2"
            disabled={files.length < 2}
          >
            <SplitSquareHorizontal className="h-3 w-3" />
          </Button>
          <Button
            variant={splitMode === 'vertical' ? 'default' : 'ghost'}
            size="sm"
            onClick={toggleVerticalSplit}
            className="h-7 px-2"
            disabled={files.length < 2}
          >
            <SplitSquareVertical className="h-3 w-3" />
          </Button>
          <Button
            variant={splitMode === 'single' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => {
              setSplitMode('single');
              setPanes(prev => prev.slice(0, 1));
            }}
            className="h-7 px-2"
          >
            <Maximize2 className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Editor panes */}
      <div className={cn(
        'flex-1 flex min-h-0',
        splitMode === 'horizontal' ? 'flex-row' : 'flex-col'
      )}>
        {panes.map((pane, index) => (
          <React.Fragment key={pane.id}>
            {renderPane(pane, index)}
            {index < panes.length - 1 && (
              <div className={cn(
                'bg-border',
                splitMode === 'horizontal' ? 'w-px' : 'h-px'
              )} />
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}
