'use client';

import { useState, useEffect, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Command,
  Search,
  Save,
  Play,
  FileText,
  Zap,
  Settings,
  Palette,
  Code,
  Terminal,
  FolderOpen,
  Download,
  Upload,
  Copy,
  Scissors,
  RotateCcw,
  RotateCw,
} from 'lucide-react';

interface CommandAction {
  id: string;
  title: string;
  description: string;
  category: string;
  icon: React.ReactNode;
  shortcut?: string[];
  action: () => void;
}

interface CommandPaletteProps {
  isOpen: boolean;
  onClose: () => void;
  onSave?: () => void;
  onRun?: () => void;
  onNewFile?: () => void;
  onOpenFile?: () => void;
  onToggleSidebar?: () => void;
  onToggleTerminal?: () => void;
  onFormatCode?: () => void;
  onToggleTheme?: () => void;
}

export function CommandPalette({
  isOpen,
  onClose,
  onSave,
  onRun,
  onNewFile,
  onOpenFile,
  onToggleSidebar,
  onToggleTerminal,
  onFormatCode,
  onToggleTheme,
}: CommandPaletteProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);

  const commands: CommandAction[] = [
    // File operations
    {
      id: 'save-file',
      title: 'Save File',
      description: 'Save the current file',
      category: 'File',
      icon: <Save className="h-4 w-4" />,
      shortcut: ['Ctrl', 'S'],
      action: () => {
        onSave?.();
        onClose();
      },
    },
    {
      id: 'new-file',
      title: 'New File',
      description: 'Create a new file',
      category: 'File',
      icon: <FileText className="h-4 w-4" />,
      shortcut: ['Ctrl', 'N'],
      action: () => {
        onNewFile?.();
        onClose();
      },
    },
    {
      id: 'open-file',
      title: 'Open File',
      description: 'Open file switcher',
      category: 'File',
      icon: <FolderOpen className="h-4 w-4" />,
      shortcut: ['Ctrl', 'P'],
      action: () => {
        onOpenFile?.();
        onClose();
      },
    },
    // Code execution
    {
      id: 'run-code',
      title: 'Run Code',
      description: 'Execute the current code',
      category: 'Execution',
      icon: <Play className="h-4 w-4" />,
      shortcut: ['Ctrl', 'Enter'],
      action: () => {
        onRun?.();
        onClose();
      },
    },
    {
      id: 'run-with-analysis',
      title: 'Run with Complexity Analysis',
      description: 'Execute code with performance analysis',
      category: 'Execution',
      icon: <Zap className="h-4 w-4" />,
      shortcut: ['Ctrl', 'Shift', 'Enter'],
      action: () => {
        onRun?.();
        onClose();
      },
    },
    // Editor actions
    {
      id: 'format-code',
      title: 'Format Code',
      description: 'Format the current code',
      category: 'Editor',
      icon: <Code className="h-4 w-4" />,
      shortcut: ['Shift', 'Alt', 'F'],
      action: () => {
        onFormatCode?.();
        onClose();
      },
    },
    // View actions
    {
      id: 'toggle-sidebar',
      title: 'Toggle Sidebar',
      description: 'Show or hide the sidebar',
      category: 'View',
      icon: <FolderOpen className="h-4 w-4" />,
      shortcut: ['Ctrl', 'B'],
      action: () => {
        onToggleSidebar?.();
        onClose();
      },
    },
    {
      id: 'toggle-terminal',
      title: 'Toggle Terminal',
      description: 'Show or hide the terminal',
      category: 'View',
      icon: <Terminal className="h-4 w-4" />,
      shortcut: ['Ctrl', '`'],
      action: () => {
        onToggleTerminal?.();
        onClose();
      },
    },
    {
      id: 'toggle-theme',
      title: 'Toggle Theme',
      description: 'Switch between light and dark theme',
      category: 'View',
      icon: <Palette className="h-4 w-4" />,
      action: () => {
        onToggleTheme?.();
        onClose();
      },
    },
  ];

  const filteredCommands = commands.filter(
    (command) =>
      command.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      command.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      command.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex((prev) =>
            prev < filteredCommands.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex((prev) =>
            prev > 0 ? prev - 1 : filteredCommands.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredCommands[selectedIndex]) {
            filteredCommands[selectedIndex].action();
          }
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
      }
    },
    [isOpen, filteredCommands, selectedIndex, onClose]
  );

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  useEffect(() => {
    if (isOpen) {
      setSearchQuery('');
      setSelectedIndex(0);
    }
  }, [isOpen]);

  useEffect(() => {
    setSelectedIndex(0);
  }, [searchQuery]);

  const formatShortcut = (shortcut: string[]) => {
    return shortcut.map((key, index) => (
      <span key={index} className="inline-flex items-center">
        <Badge variant="outline" className="px-1.5 py-0.5 text-xs font-mono">
          {key}
        </Badge>
        {index < shortcut.length - 1 && (
          <span className="mx-1 text-muted-foreground">+</span>
        )}
      </span>
    ));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl p-0">
        <DialogHeader className="px-4 pt-4 pb-2">
          <DialogTitle className="flex items-center gap-2 text-base">
            <Command className="h-4 w-4" />
            Command Palette
          </DialogTitle>
        </DialogHeader>
        <div className="px-4 pb-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Type a command or search..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9"
              autoFocus
            />
          </div>
        </div>
        <ScrollArea className="max-h-96">
          <div className="px-2 pb-2">
            {filteredCommands.length > 0 ? (
              <div className="space-y-1">
                {filteredCommands.map((command, index) => (
                  <div
                    key={command.id}
                    className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors ${
                      index === selectedIndex
                        ? 'bg-accent text-accent-foreground'
                        : 'hover:bg-muted/50'
                    }`}
                    onClick={command.action}
                  >
                    <div className="flex items-center gap-3">
                      <div className="text-muted-foreground">
                        {command.icon}
                      </div>
                      <div>
                        <div className="text-sm font-medium">
                          {command.title}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {command.description}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {command.category}
                      </Badge>
                      {command.shortcut && (
                        <div className="flex items-center gap-1">
                          {formatShortcut(command.shortcut)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No commands found</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
