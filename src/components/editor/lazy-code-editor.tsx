'use client';

import { Suspense, lazy } from 'react';
import { Loader2 } from 'lucide-react';

// <PERSON>zy load the Monaco Editor to improve initial page load
const CodeEditor = lazy(() => 
  import('./code-editor').then(module => ({ default: module.CodeEditor }))
);

interface LazyCodeEditorProps {
  value: string;
  language: string;
  onChange?: (value: string) => void;
  onSave?: () => void;
  readOnly?: boolean;
  className?: string;
  onKeyDown?: (e: KeyboardEvent) => void;
  showMinimap?: boolean;
  fontSize?: number;
  wordWrap?: 'on' | 'off' | 'wordWrapColumn' | 'bounded';
}

// Loading component for the editor
function EditorSkeleton() {
  return (
    <div className="flex items-center justify-center h-full bg-muted/20 rounded-lg border border-border/50">
      <div className="flex flex-col items-center gap-3 text-muted-foreground">
        <Loader2 className="h-8 w-8 animate-spin" />
        <div className="text-sm font-medium">Loading Editor...</div>
        <div className="text-xs">Initializing Monaco Editor</div>
      </div>
    </div>
  );
}

export function LazyCodeEditor(props: LazyCodeEditorProps) {
  return (
    <Suspense fallback={<EditorSkeleton />}>
      <CodeEditor {...props} />
    </Suspense>
  );
}
