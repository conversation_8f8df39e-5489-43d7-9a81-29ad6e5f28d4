'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import { LazyCodeEditor } from './lazy-code-editor';
import { KeyboardShortcutsPanel } from './keyboard-shortcuts-panel';
import { CommandPalette } from './command-palette';
import { formatCode } from '@/lib/code-formatting/formatter';
import { lintCode } from '@/lib/linting/linter';
import { runTests, createTestSuite, generateTestCases } from '@/lib/testing/test-runner';
import { getLanguageConfig, languageSupportsFeature } from '@/lib/languages/language-config';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ResizablePanel } from '@/components/ui/resizable-panel';
import { CodeExecutionLoadingState } from '@/components/ui/loading-states';
import { InputVariablesPanel } from './input-variables-panel';
import { ComplexityResultsPanel } from './complexity-results-panel';
import { ComplexityResult } from '@/lib/complexity-analysis/types';
import {
  Play,
  Square,
  RotateCcw,
  Terminal,
  Clock,
  Loader2,
  Maximize2,
  Minimize2,
  BarChart3,
  TrendingUp,
  Command,
  Settings,
  Map,
  Code,
  Zap,
} from 'lucide-react';

interface EnhancedCodeEditorProps {
  value: string;
  language: string;
  onChange?: (value: string) => void;
  onSave?: () => void;
  onExecutionStart?: () => void;
  onExecutionComplete?: (result: ExecutionResult) => void;
  readOnly?: boolean;
  fileId?: string;
}

interface ExecutionResult {
  output: string;
  error?: string;
  executionTime: number;
  status: 'success' | 'error' | 'timeout';
  complexityResult?: ComplexityResult;
}

// ComplexityResult, ComplexityClass, PerformanceMeasurement, and OptimizationSuggestion
// are now imported from @/lib/complexity-analysis/types

// Complexity analysis function
async function runComplexityAnalysis(
  code: string,
  language: string
): Promise<ComplexityResult> {
  try {
    // Import the complexity analysis controller
    const { defaultWorkflow } = await import(
      '@/lib/complexity-analysis/controller'
    );

    // Run the analysis
    const analysisResult = await defaultWorkflow.runAnalysis(code, language);

    return analysisResult.result;
  } catch (error) {
    console.error('Complexity analysis failed:', error);
    throw error;
  }
}

export function EnhancedCodeEditor({
  value,
  language,
  onChange,
  onSave,
  onExecutionStart,
  onExecutionComplete,
  readOnly = false,
  fileId,
}: EnhancedCodeEditorProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [executionResult, setExecutionResult] =
    useState<ExecutionResult | null>(null);
  const [, setExecutionHistory] = useState<ExecutionResult[]>([]);
  const [isOutputExpanded, setIsOutputExpanded] = useState(true);
  const [userInputs, setUserInputs] = useState<string[]>([]);
  const [showInputDialog, setShowInputDialog] = useState(false);
  const [inputPrompt, setInputPrompt] = useState('');
  const [currentInput, setCurrentInput] = useState('');
  const [variablesText, setVariablesText] = useState(() => {
    // Initialize with default variables based on language
    if (language === 'python') {
      return `nums = [2, 7, 11, 15]\ntarget = 9`;
    } else {
      return `const nums = [2, 7, 11, 15];\nconst target = 9;`;
    }
  });
  const [pythonStatus, setPythonStatus] = useState<
    'loading' | 'ready' | 'error'
  >('loading');
  const [isComplexityAnalysisEnabled, setIsComplexityAnalysisEnabled] =
    useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showComplexityPanel, setShowComplexityPanel] = useState(false);
  const [showCommandPalette, setShowCommandPalette] = useState(false);
  const [showMinimap, setShowMinimap] = useState(true);
  const [editorFontSize, setEditorFontSize] = useState(14);
  const [lintIssues, setLintIssues] = useState<any[]>([]);
  const [isFormatting, setIsFormatting] = useState(false);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const isExecutable = (lang: string) => {
    return ['javascript', 'typescript', 'python'].includes(lang.toLowerCase());
  };

  const getRunButtonText = () => {
    if (language === 'typescript') return 'Run TypeScript';
    if (language === 'javascript') return 'Run JavaScript';
    if (language === 'python') return 'Run Python';
    return 'Run Code';
  };

  // Handle code formatting
  const handleFormatCode = useCallback(async () => {
    if (!value.trim()) return;

    setIsFormatting(true);
    try {
      const result = await formatCode(value, language);
      if (result.success && result.formattedCode) {
        onChange?.(result.formattedCode);
      } else {
        console.error('Formatting failed:', result.error);
      }
    } catch (error) {
      console.error('Formatting error:', error);
    } finally {
      setIsFormatting(false);
    }
  }, [value, language, onChange]);

  // Handle code linting
  const handleLintCode = useCallback(async () => {
    if (!value.trim()) {
      setLintIssues([]);
      return;
    }

    try {
      const result = await lintCode(value, language);
      setLintIssues(result.issues);
    } catch (error) {
      console.error('Linting error:', error);
    }
  }, [value, language]);

  // Handle test execution
  const handleRunTests = useCallback(async () => {
    if (!value.trim()) return;

    setIsRunningTests(true);
    try {
      const testCases = generateTestCases('solution', language);
      const testSuite = createTestSuite('Code Tests', testCases);
      const results = await runTests(value, language, testSuite);
      setTestResults(results);
    } catch (error) {
      console.error('Test execution error:', error);
    } finally {
      setIsRunningTests(false);
    }
  }, [value, language]);

  const handleExecuteCode = useCallback(async () => {
    if (!value.trim() || isRunning) return;

    setIsRunning(true);
    abortControllerRef.current = new AbortController();

    // Notify that execution is starting
    onExecutionStart?.();

    try {
      const startTime = Date.now();

      // Combine variables with the main code
      const codeToExecute = variablesText.trim()
        ? `${variablesText}\n\n${value}`
        : value;

      const result = await executeCode(
        codeToExecute,
        language,
        abortControllerRef.current.signal,
        userInputs
      );
      const executionTime = Date.now() - startTime;

      let complexityResult: ComplexityResult | undefined;

      // Run complexity analysis if enabled
      if (isComplexityAnalysisEnabled && result.status === 'success') {
        try {
          setIsAnalyzing(true);

          // Add timeout to prevent hanging
          const analysisPromise = runComplexityAnalysis(
            codeToExecute,
            language
          );
          const timeoutPromise = new Promise<never>((_, reject) =>
            setTimeout(
              () => reject(new Error('Analysis timeout after 30 seconds')),
              30000
            )
          );

          complexityResult = await Promise.race([
            analysisPromise,
            timeoutPromise,
          ]);
        } catch (analysisError) {
          console.warn('Complexity analysis failed:', analysisError);
          // Continue with normal execution result even if analysis fails
        } finally {
          setIsAnalyzing(false);
        }
      }

      const fullResult: ExecutionResult = {
        ...result,
        executionTime,
        complexityResult,
      };

      // Notify that execution is complete
      onExecutionComplete?.(fullResult);

      setExecutionResult(fullResult);
      setExecutionHistory((prev) => [fullResult, ...prev.slice(0, 9)]); // Keep last 10 results

      // Always expand output panel when code runs
      setIsOutputExpanded(true);

      // Auto-open complexity panel if analysis completed successfully
      if (
        isComplexityAnalysisEnabled &&
        complexityResult &&
        complexityResult.executionSuccess
      ) {
        setShowComplexityPanel(true);
      }
    } catch (error: any) {
      const errorResult: ExecutionResult = {
        output: '',
        error: error.message,
        executionTime: Date.now() - Date.now(),
        status: 'error',
      };

      // Notify that execution is complete (with error)
      onExecutionComplete?.(errorResult);

      setExecutionResult(errorResult);
      setExecutionHistory((prev) => [errorResult, ...prev.slice(0, 9)]);

      // Always expand output panel to show errors
      setIsOutputExpanded(true);
    } finally {
      setIsRunning(false);
      setIsAnalyzing(false);
      abortControllerRef.current = null;
    }
  }, [
    value,
    language,
    isRunning,
    isOutputExpanded,
    userInputs,
    variablesText,
    isComplexityAnalysisEnabled,
    onExecutionStart,
    onExecutionComplete,
  ]);

  const stopExecution = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setIsRunning(false);
    }
  }, []);

  const clearOutput = useCallback(() => {
    setExecutionResult(null);
  }, []);

  const handleInputSubmit = useCallback(() => {
    if (currentInput.trim()) {
      setUserInputs((prev) => [...prev, currentInput.trim()]);
      setCurrentInput('');
      setShowInputDialog(false);
    }
  }, [currentInput]);

  const handleInputCancel = useCallback(() => {
    setCurrentInput('');
    setShowInputDialog(false);
  }, []);

  const requestUserInput = useCallback((prompt: string = 'Enter input:') => {
    setInputPrompt(prompt);
    setShowInputDialog(true);
  }, []);

  // Keyboard shortcut for running code and closing panel
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        handleExecuteCode();
      }
      if (e.key === 'Escape' && showComplexityPanel) {
        e.preventDefault();
        setShowComplexityPanel(false);
      }
      // Ctrl+Shift+P to open command palette
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
        e.preventDefault();
        setShowCommandPalette(true);
      }
      // Ctrl+Shift+Enter to run with complexity analysis
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'Enter') {
        e.preventDefault();
        setIsComplexityAnalysisEnabled(true);
        handleExecuteCode();
      }
    },
    [handleExecuteCode, showComplexityPanel]
  );

  // Update variables when language changes
  useEffect(() => {
    if (language === 'python') {
      setVariablesText(`nums = [2, 7, 11, 15]\ntarget = 9`);
    } else {
      setVariablesText(`const nums = [2, 7, 11, 15];\nconst target = 9;`);
    }
  }, [language]);

  // Auto-lint code when it changes (debounced)
  useEffect(() => {
    if (!languageSupportsFeature(language, 'linting')) return;

    const timeoutId = setTimeout(() => {
      handleLintCode();
    }, 1000); // Debounce for 1 second

    return () => clearTimeout(timeoutId);
  }, [value, language, handleLintCode]);

  // Check Python status and trigger loading when Python file is opened
  useEffect(() => {
    if (language === 'python') {
      // Trigger Python loading when Python file is opened
      if (
        typeof window !== 'undefined' &&
        !(window as any).pyodide &&
        !(window as any).pyodideLoading &&
        (window as any).loadPyodideOnDemand
      ) {
        console.log('🐍 Python file detected, starting Pyodide loading...');
        (window as any).loadPyodideOnDemand().catch(console.error);
      }

      const checkPythonStatus = () => {
        if (typeof window !== 'undefined') {
          if ((window as any).pyodide) {
            setPythonStatus('ready');
          } else if ((window as any).pyodideLoading) {
            setPythonStatus('loading');
          } else {
            setPythonStatus('loading');
          }
        }
      };

      checkPythonStatus();
      const interval = setInterval(checkPythonStatus, 2000);
      return () => clearInterval(interval);
    }
  }, [language]);

  return (
    <div className="flex flex-col h-full min-h-0">
      {/* Enhanced Toolbar with Better Button Grouping */}
      <div className="flex items-center justify-between border-b border-border px-2 sm:px-4 py-2 sm:py-3 flex-shrink-0 bg-muted/20 shadow-sm overflow-x-auto">
        <div className="flex items-center gap-2 sm:gap-3 min-w-0">
          {/* Primary Action Group - Run Controls */}
          <div className="flex items-center gap-2 p-1 rounded-lg bg-background border border-border/50">
            {isRunning ? (
              <Button
                size="sm"
                variant="destructive"
                onClick={stopExecution}
                className="run-button flex items-center gap-2 font-medium"
                aria-label="Stop code execution"
                aria-keyshortcuts="Escape"
              >
                <Square className="h-3.5 w-3.5" aria-hidden="true" />
                Stop
              </Button>
            ) : (
              <Button
                size="sm"
                variant={isExecutable(language) ? 'success' : 'secondary'}
                onClick={handleExecuteCode}
                disabled={
                  !value.trim() ||
                  (language === 'python' && pythonStatus !== 'ready')
                }
                aria-label={
                  isExecutable(language)
                    ? `Execute ${language} code`
                    : `${language} execution not supported in browser`
                }
                aria-keyshortcuts="Ctrl+Enter"
                aria-describedby={
                  language === 'python' && pythonStatus !== 'ready'
                    ? 'python-status-info'
                    : undefined
                }
                title={
                  isExecutable(language)
                    ? `Execute ${language} code (Ctrl+Enter)`
                    : `${language} execution not supported in browser`
                }
                className="run-button flex items-center gap-2 font-medium"
              >
                <Play className="h-3.5 w-3.5" aria-hidden="true" />
                {getRunButtonText()}
              </Button>
            )}

            {/* Clear Output */}
            <Button
              size="sm"
              variant="outline"
              onClick={clearOutput}
              disabled={!executionResult}
              title="Clear output (Ctrl+K)"
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-3.5 w-3.5" />
              <span className="hidden sm:inline">Clear</span>
            </Button>
          </div>

          {/* Secondary Action Group - Input Controls */}
          {language === 'python' && (
            <div className="flex items-center gap-2 p-1 rounded-lg bg-background border border-border/50">
              <Button
                size="sm"
                variant="outline"
                onClick={() =>
                  requestUserInput('Enter input for your Python program:')
                }
                title="Add input for Python program"
                className="flex items-center gap-2"
              >
                <Terminal className="h-3.5 w-3.5" />
                <span className="hidden sm:inline">Add Input</span>
                {userInputs.length > 0 && (
                  <Badge variant="secondary" className="ml-1 text-xs">
                    {userInputs.length}
                  </Badge>
                )}
              </Button>
            </div>
          )}

          {/* Complexity Analysis Group */}
          {isExecutable(language) && (
            <div className="flex items-center gap-2 p-1 rounded-lg bg-background border border-border/50">
              <Button
                size="sm"
                variant={isComplexityAnalysisEnabled ? 'default' : 'outline'}
                onClick={() => {
                  if (
                    isComplexityAnalysisEnabled &&
                    executionResult?.complexityResult
                  ) {
                    // If analysis is enabled and we have results, toggle panel
                    setShowComplexityPanel(!showComplexityPanel);
                  } else {
                    // Toggle analysis on/off
                    const newState = !isComplexityAnalysisEnabled;
                    setIsComplexityAnalysisEnabled(newState);
                    setShowComplexityPanel(false); // Always close panel when disabling
                  }
                }}
                title={
                  isComplexityAnalysisEnabled &&
                  executionResult?.complexityResult
                    ? `${
                        showComplexityPanel ? 'Hide' : 'Show'
                      } complexity analysis results`
                    : `${
                        isComplexityAnalysisEnabled ? 'Disable' : 'Enable'
                      } complexity analysis`
                }
                className="flex items-center gap-2"
                disabled={isRunning}
              >
                {isAnalyzing ? (
                  <Loader2 className="h-3.5 w-3.5 animate-spin" />
                ) : (
                  <BarChart3 className="h-3.5 w-3.5" />
                )}
                <span className="hidden sm:inline">
                  {isAnalyzing ? 'Analyzing...' : 'Analysis'}
                </span>
                {isComplexityAnalysisEnabled &&
                  executionResult?.complexityResult &&
                  !isAnalyzing && (
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                  )}
              </Button>
            </div>
          )}
        </div>

        {/* Right Section - Language Info and Status */}
        <div className="flex items-center gap-2 sm:gap-3 ml-2 flex-shrink-0">
          {/* Language Status Indicator */}
          <div className="flex items-center gap-2 px-3 py-1.5 bg-background rounded-lg border border-border/50">
            <div
              className={`w-2.5 h-2.5 rounded-full transition-colors ${
                language === 'python'
                  ? pythonStatus === 'ready'
                    ? 'bg-success animate-pulse'
                    : pythonStatus === 'loading'
                    ? 'bg-warning animate-pulse'
                    : 'bg-error'
                  : isExecutable(language)
                  ? 'bg-success'
                  : 'bg-warning'
              }`}
              title={
                language === 'python'
                  ? `Python ${pythonStatus}`
                  : isExecutable(language)
                  ? `${language} ready`
                  : `${language} (view only)`
              }
            ></div>
            <span className="font-medium capitalize text-foreground text-sm">
              {language}
            </span>
            {language === 'python' && (
              <span
                id="python-status-info"
                className={`text-xs transition-colors ${
                  pythonStatus === 'ready'
                    ? 'text-success'
                    : pythonStatus === 'loading'
                    ? 'text-warning'
                    : 'text-error'
                }`}
                role="status"
                aria-live="polite"
              >
                {pythonStatus === 'loading' && (
                  <Loader2
                    className="h-3 w-3 animate-spin inline mr-1"
                    aria-hidden="true"
                  />
                )}
                ({pythonStatus})
              </span>
            )}
            {!isExecutable(language) && language !== 'python' && (
              <span className="text-xs text-muted-foreground">(View only)</span>
            )}
          </div>

          {/* Execution Status */}
          {executionResult && (
            <div className="flex items-center gap-2 px-3 py-1.5 bg-background rounded-lg border border-border/50">
              <Badge
                variant={
                  executionResult.status === 'success'
                    ? 'default'
                    : 'destructive'
                }
                className={`font-medium text-xs ${
                  executionResult.status === 'success'
                    ? 'bg-success/10 text-success border-success/20'
                    : 'bg-error/10 text-error border-error/20'
                }`}
              >
                {executionResult.status === 'success' ? '✓ Success' : '✗ Error'}
              </Badge>
              <div className="flex items-center gap-1 text-muted-foreground text-xs">
                <Clock className="h-3 w-3" />
                <span className="font-mono font-medium">
                  {executionResult.executionTime}ms
                </span>
              </div>
            </div>
          )}

          {/* Output Toggle - Enhanced with better grouping */}
          <div className="flex items-center gap-2 p-1 rounded-lg bg-background border border-border/50">
            <Button
              size="sm"
              variant={isOutputExpanded ? 'secondary' : 'outline'}
              onClick={() => setIsOutputExpanded(!isOutputExpanded)}
              className="flex items-center gap-2 font-medium"
              title={`${isOutputExpanded ? 'Hide' : 'Show'} console output`}
            >
              <Terminal className="h-3.5 w-3.5" />
              <span className="hidden sm:inline">
                {isOutputExpanded ? 'Hide' : 'Show'} Console
              </span>
              {isOutputExpanded ? (
                <Minimize2 className="h-3 w-3" />
              ) : (
                <Maximize2 className="h-3 w-3" />
              )}
            </Button>
          </div>
        </div>

        {/* Right side toolbar controls */}
        <div className="flex items-center gap-2">
          {/* Code Tools Group */}
          <div className="flex items-center gap-1 p-1 rounded-lg bg-background border border-border/50">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleFormatCode}
              disabled={isFormatting || !languageSupportsFeature(language, 'formatting')}
              title="Format code (Shift+Alt+F)"
              className="p-2"
            >
              <Code className="h-3.5 w-3.5" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRunTests}
              disabled={isRunningTests || !languageSupportsFeature(language, 'testing')}
              title="Run tests"
              className="p-2"
            >
              <Zap className="h-3.5 w-3.5" />
            </Button>
          </div>

          {/* Editor Settings Group */}
          <div className="flex items-center gap-1 p-1 rounded-lg bg-background border border-border/50">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowMinimap(!showMinimap)}
              title={`${showMinimap ? 'Hide' : 'Show'} minimap`}
              className="p-2"
            >
              <Map className="h-3.5 w-3.5" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowCommandPalette(true)}
              title="Command palette (Ctrl+Shift+P)"
              className="p-2"
            >
              <Command className="h-3.5 w-3.5" />
            </Button>
          </div>

          {/* Keyboard Shortcuts */}
          <KeyboardShortcutsPanel />
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex min-h-0">
        {/* Code Editor - Left side */}
        <div className="flex-1 min-w-[40%] w-[60%] min-h-0">
          <LazyCodeEditor
            value={value}
            language={language}
            onChange={onChange}
            onSave={onSave}
            readOnly={readOnly}
            className="h-full"
            onKeyDown={handleKeyDown}
            showMinimap={showMinimap}
            fontSize={editorFontSize}
          />
        </div>

        {/* Right Panel - Input Variables and Output Panel */}
        {(language === 'javascript' ||
          language === 'typescript' ||
          language === 'python') && (
          <ResizablePanel
            defaultSize={500}
            minSize={350}
            maxSize={1000}
            className="border-l border-border bg-background flex flex-col"
          >
            {/* Input Variables Panel - Top section with fixed height */}
            <div
              className="flex-shrink-0"
              style={{
                height: '220px',
                minHeight: '220px',
                maxHeight: '220px',
              }}
            >
              <InputVariablesPanel
                language={language}
                onVariablesChange={setVariablesText}
                onRunWithVariables={() => {
                  // Run code with the variables
                  handleExecuteCode();
                }}
                className="h-full"
              />
            </div>

            {/* Substantial divider with spacing */}
            <div className="flex-shrink-0 h-8 flex items-center bg-muted/20 border-y border-border">
              <div className="w-full h-full"></div>
            </div>

            {/* Output Panel - Bottom section with flexible height */}
            <div
              className="flex-1 flex flex-col"
              style={{ minHeight: '300px', height: 'calc(100% - 220px - 8px)' }}
            >
              {/* Output Header - More prominent */}
              <div className="flex-shrink-0 flex items-center justify-between px-4 py-3 bg-muted/30 border-b border-border">
                <div className="flex items-center gap-2">
                  <Terminal className="h-4 w-4 text-primary" />
                  <span className="font-semibold text-sm">Output</span>
                  {executionResult && (
                    <Badge
                      variant={
                        executionResult.status === 'success'
                          ? 'default'
                          : 'destructive'
                      }
                      className="text-xs"
                    >
                      {executionResult.status}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {executionResult && (
                    <span className="text-xs text-muted-foreground">
                      {executionResult.executionTime}ms
                    </span>
                  )}
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={clearOutput}
                    disabled={!executionResult}
                    className="h-6 w-6 p-0"
                    title="Clear output"
                  >
                    <RotateCcw className="h-3 w-3" />
                  </Button>
                </div>
              </div>

              {/* Output Content - Simplified scrolling structure */}
              <div className="flex-1 overflow-auto output-scroll">
                <div className="p-4">
                  {isRunning ? (
                    <div className="flex items-center gap-3 text-muted-foreground p-3 bg-muted/30 rounded-lg">
                      <Loader2 className="h-4 w-4 animate-spin text-primary" />
                      <span className="text-sm font-medium">
                        Executing code...
                      </span>
                    </div>
                  ) : executionResult ? (
                    <div className="space-y-4">
                      {executionResult.output ? (
                        <div>
                          <div className="bg-slate-950 text-green-400 p-4 rounded-lg border shadow-inner overflow-auto">
                            <pre className="text-sm whitespace-pre-wrap font-mono leading-relaxed break-words max-w-full">
                              {executionResult.output}
                            </pre>
                          </div>
                        </div>
                      ) : null}
                      {executionResult.error ? (
                        <div>
                          <div className="bg-red-950/20 text-red-400 p-4 rounded-lg border border-red-500/20 shadow-inner">
                            <pre className="text-sm whitespace-pre-wrap font-mono leading-relaxed break-words">
                              {executionResult.error}
                            </pre>
                          </div>
                        </div>
                      ) : null}
                      {!executionResult.output && !executionResult.error ? (
                        <div className="text-center text-muted-foreground py-6">
                          <div className="w-12 h-12 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-3">
                            <Terminal className="h-5 w-5" />
                          </div>
                          <p className="text-sm font-medium">
                            No output produced
                          </p>
                          <p className="text-xs mt-1 opacity-75">
                            Code executed successfully but produced no console
                            output
                          </p>
                        </div>
                      ) : null}

                      {/* Complexity analysis moved to dedicated panel */}
                    </div>
                  ) : (
                    <div className="text-center text-muted-foreground py-8">
                      <div className="w-16 h-16 bg-muted/30 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Terminal className="h-7 w-7" />
                      </div>
                      <h3 className="font-medium mb-2">Output Ready</h3>
                      <p className="text-sm mb-1">
                        {isExecutable(language)
                          ? 'Click "Run" or press Ctrl+Enter to execute your code'
                          : `${language} files can be viewed and edited, but cannot be executed in the browser`}
                      </p>
                      {isExecutable(language) && (
                        <div className="text-xs text-muted-foreground/75 mt-2">
                          {language === 'python'
                            ? 'Supports: print(), input(), and most Python features'
                            : 'Supports: console.log(), prompt(), alert(), and most JavaScript features'}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </ResizablePanel>
        )}
      </div>

      {/* Complexity Analysis Modal */}
      {showComplexityPanel && (
        <div
          className="modal-overlay"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setShowComplexityPanel(false);
            }
          }}
        >
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            {/* Panel Header */}
            <div className="flex items-center justify-between p-4 border-b border-border bg-muted/30">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-primary" />
                <h2 className="font-semibold">Complexity Analysis</h2>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowComplexityPanel(false)}
                className="h-8 w-8 p-0"
              >
                <span className="sr-only">Close</span>×
              </Button>
            </div>

            {/* Panel Content */}
            <div className="flex-1 overflow-auto">
              <ComplexityResultsPanel
                result={executionResult?.complexityResult || null}
                isAnalyzing={isAnalyzing}
                className="h-full"
              />
            </div>
          </div>
        </div>
      )}

      {/* Input Dialog */}
      {showInputDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-card border border-border rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4">Python Input</h3>
            <p className="text-sm text-muted-foreground mb-4">{inputPrompt}</p>
            <input
              type="text"
              value={currentInput}
              onChange={(e) => setCurrentInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleInputSubmit();
                } else if (e.key === 'Escape') {
                  handleInputCancel();
                }
              }}
              placeholder="Enter your input..."
              className="w-full px-3 py-2 border border-border rounded-md bg-background"
              autoFocus
            />
            {userInputs.length > 0 && (
              <div className="mt-3">
                <p className="text-xs text-muted-foreground mb-2">
                  Current inputs:
                </p>
                <div className="flex flex-wrap gap-1">
                  {userInputs.map((input, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-muted text-xs rounded"
                    >
                      {input}
                    </span>
                  ))}
                </div>
              </div>
            )}
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={handleInputCancel}>
                Cancel
              </Button>
              <Button
                onClick={handleInputSubmit}
                disabled={!currentInput.trim()}
              >
                Add Input
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Command Palette */}
      <CommandPalette
        isOpen={showCommandPalette}
        onClose={() => setShowCommandPalette(false)}
        onSave={onSave}
        onRun={handleExecuteCode}
        onFormatCode={handleFormatCode}
        onToggleTheme={() => {
          // TODO: Implement theme toggle
          console.log('Toggle theme requested');
        }}
      />
    </div>
  );
}

// Real code execution function
async function executeCode(
  code: string,
  language: string,
  signal: AbortSignal,
  userInputs: string[] = []
): Promise<Omit<ExecutionResult, 'executionTime'>> {
  if (signal.aborted) {
    throw new Error('Execution aborted');
  }

  try {
    let result;

    if (language === 'javascript') {
      const { BrowserCodeExecutor } = await import(
        '@/lib/code-execution/browser-executor'
      );
      result = await BrowserCodeExecutor.executeJavaScript(code);
    } else if (language === 'typescript') {
      const { BrowserCodeExecutor } = await import(
        '@/lib/code-execution/browser-executor'
      );
      result = await BrowserCodeExecutor.executeTypeScript(code);
    } else if (language === 'python') {
      const { BrowserCodeExecutor } = await import(
        '@/lib/code-execution/browser-executor'
      );
      result = await BrowserCodeExecutor.executePython(code, userInputs);
    } else {
      result = {
        output: `Code execution for ${language} is not yet supported in browser mode.`,
        error: `Currently only JavaScript, TypeScript, and Python are supported for browser-based execution.\n\nTo run ${language} code, you would need server-side execution setup.`,
        executionTime: 0,
      };
    }

    return {
      output: result.output,
      error: result.error,
      status: result.error ? 'error' : 'success',
    };
  } catch (error: any) {
    return {
      output: '',
      error: error.message,
      status: 'error',
    };
  }
}
