'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Keyboard, Command, Search, Save, Play, FileText, Zap } from 'lucide-react';

interface KeyboardShortcut {
  category: string;
  shortcuts: {
    keys: string[];
    description: string;
    icon?: React.ReactNode;
  }[];
}

const KEYBOARD_SHORTCUTS: KeyboardShortcut[] = [
  {
    category: 'File Operations',
    shortcuts: [
      {
        keys: ['Ctrl', 'S'],
        description: 'Save current file',
        icon: <Save className="h-4 w-4" />,
      },
      {
        keys: ['Ctrl', 'N'],
        description: 'Create new file',
        icon: <FileText className="h-4 w-4" />,
      },
      {
        keys: ['Ctrl', 'P'],
        description: 'Quick file switcher',
        icon: <Search className="h-4 w-4" />,
      },
      {
        keys: ['Ctrl', 'W'],
        description: 'Close current file',
        icon: <FileText className="h-4 w-4" />,
      },
    ],
  },
  {
    category: 'Code Execution',
    shortcuts: [
      {
        keys: ['Ctrl', 'Enter'],
        description: 'Run code',
        icon: <Play className="h-4 w-4" />,
      },
      {
        keys: ['Ctrl', 'Shift', 'Enter'],
        description: 'Run with complexity analysis',
        icon: <Zap className="h-4 w-4" />,
      },
      {
        keys: ['Ctrl', 'Shift', 'C'],
        description: 'Stop execution',
        icon: <Command className="h-4 w-4" />,
      },
    ],
  },
  {
    category: 'Editor',
    shortcuts: [
      {
        keys: ['Ctrl', '/'],
        description: 'Toggle line comment',
        icon: <Command className="h-4 w-4" />,
      },
      {
        keys: ['Ctrl', 'D'],
        description: 'Duplicate line',
        icon: <Command className="h-4 w-4" />,
      },
      {
        keys: ['Alt', 'Up'],
        description: 'Move line up',
        icon: <Command className="h-4 w-4" />,
      },
      {
        keys: ['Alt', 'Down'],
        description: 'Move line down',
        icon: <Command className="h-4 w-4" />,
      },
      {
        keys: ['Ctrl', 'Shift', 'K'],
        description: 'Delete line',
        icon: <Command className="h-4 w-4" />,
      },
      {
        keys: ['Ctrl', 'F'],
        description: 'Find in file',
        icon: <Search className="h-4 w-4" />,
      },
      {
        keys: ['Ctrl', 'H'],
        description: 'Find and replace',
        icon: <Search className="h-4 w-4" />,
      },
    ],
  },
  {
    category: 'Navigation',
    shortcuts: [
      {
        keys: ['Ctrl', 'G'],
        description: 'Go to line',
        icon: <Command className="h-4 w-4" />,
      },
      {
        keys: ['Ctrl', 'Shift', 'O'],
        description: 'Go to symbol',
        icon: <Command className="h-4 w-4" />,
      },
      {
        keys: ['F12'],
        description: 'Go to definition',
        icon: <Command className="h-4 w-4" />,
      },
    ],
  },
  {
    category: 'View',
    shortcuts: [
      {
        keys: ['Ctrl', 'Shift', 'P'],
        description: 'Command palette',
        icon: <Command className="h-4 w-4" />,
      },
      {
        keys: ['Ctrl', 'B'],
        description: 'Toggle sidebar',
        icon: <Command className="h-4 w-4" />,
      },
      {
        keys: ['Ctrl', '`'],
        description: 'Toggle terminal',
        icon: <Command className="h-4 w-4" />,
      },
      {
        keys: ['F11'],
        description: 'Toggle fullscreen',
        icon: <Command className="h-4 w-4" />,
      },
    ],
  },
];

interface KeyboardShortcutsPanelProps {
  trigger?: React.ReactNode;
}

export function KeyboardShortcutsPanel({ trigger }: KeyboardShortcutsPanelProps) {
  const [isOpen, setIsOpen] = useState(false);

  const defaultTrigger = (
    <Button variant="ghost" size="sm" className="gap-2">
      <Keyboard className="h-4 w-4" />
      Shortcuts
    </Button>
  );

  const formatKeys = (keys: string[]) => {
    return keys.map((key, index) => (
      <span key={index} className="inline-flex items-center">
        <Badge variant="outline" className="px-2 py-1 text-xs font-mono">
          {key}
        </Badge>
        {index < keys.length - 1 && (
          <span className="mx-1 text-muted-foreground">+</span>
        )}
      </span>
    ));
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Keyboard className="h-5 w-5" />
            Keyboard Shortcuts
          </DialogTitle>
        </DialogHeader>
        <ScrollArea className="max-h-[60vh] pr-4">
          <div className="space-y-6">
            {KEYBOARD_SHORTCUTS.map((category) => (
              <div key={category.category}>
                <h3 className="text-sm font-semibold text-foreground mb-3 border-b pb-1">
                  {category.category}
                </h3>
                <div className="space-y-2">
                  {category.shortcuts.map((shortcut, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between py-2 px-3 rounded-lg hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        {shortcut.icon && (
                          <div className="text-muted-foreground">
                            {shortcut.icon}
                          </div>
                        )}
                        <span className="text-sm text-foreground">
                          {shortcut.description}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        {formatKeys(shortcut.keys)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
        <div className="flex justify-end pt-4 border-t">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
