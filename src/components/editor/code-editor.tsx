'use client';

import { useEffect, useRef } from 'react';
import Editor from '@monaco-editor/react';
import type { editor } from 'monaco-editor';
import { cn } from '@/lib/utils';
import { useTheme } from '@/contexts/theme-context';

interface CodeEditorProps {
  value: string;
  language: string;
  onChange?: (value: string) => void;
  onSave?: () => void;
  readOnly?: boolean;
  className?: string;
  onKeyDown?: (e: KeyboardEvent) => void;
  showMinimap?: boolean;
  fontSize?: number;
  wordWrap?: 'on' | 'off' | 'wordWrapColumn' | 'bounded';
}

export function CodeEditor({
  value,
  language,
  onChange,
  onSave,
  readOnly = false,
  className,
  onKeyDown,
  showMinimap = true,
  fontSize = 14,
  wordWrap = 'on',
}: CodeEditorProps) {
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  const { editorTheme: theme } = useTheme();

  const handleEditorDidMount = (editor: editor.IStandaloneCodeEditor) => {
    editorRef.current = editor;

    // Add keyboard shortcuts
    editor.addCommand(
      // Ctrl+S / Cmd+S
      2048 + 49, // Monaco.KeyMod.CtrlCmd | Monaco.KeyCode.KeyS
      () => {
        onSave?.();
      }
    );

    // Add Ctrl+Enter for code execution
    if (onKeyDown) {
      editor.addCommand(
        2048 + 3, // Monaco.KeyMod.CtrlCmd | Monaco.KeyCode.Enter
        () => {
          const event = new KeyboardEvent('keydown', {
            key: 'Enter',
            ctrlKey: true,
            metaKey: false,
          });
          onKeyDown(event);
        }
      );
    }

    // Add more keyboard shortcuts
    editor.addCommand(
      // Ctrl+D - Duplicate line
      2048 + 34, // Monaco.KeyMod.CtrlCmd | Monaco.KeyCode.KeyD
      () => {
        const selection = editor.getSelection();
        if (selection) {
          editor.trigger('keyboard', 'editor.action.copyLinesDownAction', {});
        }
      }
    );

    editor.addCommand(
      // Ctrl+/ - Toggle line comment
      2048 + 85, // Monaco.KeyMod.CtrlCmd | Monaco.KeyCode.Slash
      () => {
        editor.trigger('keyboard', 'editor.action.commentLine', {});
      }
    );

    editor.addCommand(
      // Alt+Up - Move line up
      512 + 16, // Monaco.KeyMod.Alt | Monaco.KeyCode.UpArrow
      () => {
        editor.trigger('keyboard', 'editor.action.moveLinesUpAction', {});
      }
    );

    editor.addCommand(
      // Alt+Down - Move line down
      512 + 18, // Monaco.KeyMod.Alt | Monaco.KeyCode.DownArrow
      () => {
        editor.trigger('keyboard', 'editor.action.moveLinesDownAction', {});
      }
    );

    editor.addCommand(
      // Ctrl+Shift+K - Delete line
      2048 + 1024 + 41, // Monaco.KeyMod.CtrlCmd | Monaco.KeyMod.Shift | Monaco.KeyCode.KeyK
      () => {
        editor.trigger('keyboard', 'editor.action.deleteLines', {});
      }
    );

    // Focus the editor
    editor.focus();
  };

  const handleChange = (value: string | undefined) => {
    if (value !== undefined) {
      onChange?.(value);
    }
  };

  const editorOptions: editor.IStandaloneEditorConstructionOptions = {
    fontSize,
    lineHeight: Math.round(fontSize * 1.5),
    fontFamily:
      'var(--font-geist-mono), "SF Mono", Monaco, Consolas, monospace',
    minimap: {
      enabled: showMinimap,
      side: 'right',
      showSlider: 'mouseover',
      renderCharacters: false,
      maxColumn: 120,
      scale: 1,
    },
    scrollBeyondLastLine: false,
    automaticLayout: true,
    wordWrap,
    renderWhitespace: 'selection',
    folding: true,
    lineNumbers: 'on',
    glyphMargin: true,
    readOnly,
    contextmenu: true,
    mouseWheelZoom: true,
    smoothScrolling: true,
    cursorBlinking: 'smooth',
    cursorSmoothCaretAnimation: 'on',
    renderLineHighlight: 'all',
    selectOnLineNumbers: true,
    roundedSelection: true,
    multiCursorModifier: 'ctrlCmd',
    formatOnPaste: true,
    formatOnType: true,
    autoIndent: 'full',
    bracketPairColorization: {
      enabled: true,
    },
    guides: {
      bracketPairs: true,
      bracketPairsHorizontal: true,
      highlightActiveBracketPair: true,
      indentation: true,
    },
    suggest: {
      showKeywords: true,
      showSnippets: true,
      showFunctions: true,
      showConstructors: true,
      showFields: true,
      showVariables: true,
      showClasses: true,
      showStructs: true,
      showInterfaces: true,
      showModules: true,
      showProperties: true,
      showEvents: true,
      showOperators: true,
      showUnits: true,
      showValues: true,
      showConstants: true,
      showEnums: true,
      showEnumMembers: true,
      showColors: true,
      showFiles: true,
      showReferences: true,
      showFolders: true,
      showTypeParameters: true,
    },
    scrollbar: {
      vertical: 'auto',
      horizontal: 'auto',
      useShadows: false,
      verticalHasArrows: false,
      horizontalHasArrows: false,
      verticalScrollbarSize: 10,
      horizontalScrollbarSize: 10,
    },
    overviewRulerBorder: false,
    hideCursorInOverviewRuler: true,
    bracketPairColorization: {
      enabled: true,
    },
    guides: {
      bracketPairs: true,
      indentation: true,
    },
    suggest: {
      showKeywords: true,
      showSnippets: true,
      showFunctions: true,
      showConstructors: true,
      showFields: true,
      showVariables: true,
      showClasses: true,
      showModules: true,
      showProperties: true,
      showEvents: true,
      showOperators: true,
      showUnits: true,
      showValues: true,
      showConstants: true,
      showEnums: true,
      showEnumMembers: true,
      showKeywords: true,
      showText: true,
      showColors: true,
      showFiles: true,
      showReferences: true,
      showFolders: true,
      showTypeParameters: true,
      showUsers: true,
      showIssues: true,
    },
  };

  return (
    <div className={cn('flex flex-col h-full min-h-0', className)}>
      <div className="flex-1 min-h-0">
        <Editor
          height="100%"
          language={language}
          value={value}
          theme={theme}
          onChange={handleChange}
          onMount={handleEditorDidMount}
          options={editorOptions}
          loading={
            <div className="flex items-center justify-center h-full min-h-[200px]">
              <div className="text-muted-foreground">Loading editor...</div>
            </div>
          }
        />
      </div>
    </div>
  );
}
