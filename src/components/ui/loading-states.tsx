'use client';

import { Loader2, Code, FileText, Zap, Database, Wifi } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  return (
    <Loader2 
      className={cn('animate-spin', sizeClasses[size], className)} 
    />
  );
}

interface LoadingSkeletonProps {
  className?: string;
  lines?: number;
}

export function LoadingSkeleton({ className, lines = 3 }: LoadingSkeletonProps) {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className={cn(
            'h-4 bg-muted rounded animate-pulse',
            i === lines - 1 ? 'w-3/4' : 'w-full'
          )}
        />
      ))}
    </div>
  );
}

interface LoadingCardProps {
  className?: string;
}

export function LoadingCard({ className }: LoadingCardProps) {
  return (
    <div className={cn('p-6 border rounded-lg bg-card', className)}>
      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-muted rounded-lg animate-pulse" />
          <div className="space-y-2 flex-1">
            <div className="h-4 bg-muted rounded animate-pulse w-1/2" />
            <div className="h-3 bg-muted rounded animate-pulse w-3/4" />
          </div>
        </div>
        <LoadingSkeleton lines={2} />
      </div>
    </div>
  );
}

interface LoadingStateProps {
  type: 'editor' | 'workspace' | 'execution' | 'file' | 'api';
  message?: string;
  progress?: number;
  className?: string;
}

export function LoadingState({ type, message, progress, className }: LoadingStateProps) {
  const getIcon = () => {
    switch (type) {
      case 'editor':
        return <Code className="h-8 w-8 text-primary" />;
      case 'workspace':
        return <FileText className="h-8 w-8 text-primary" />;
      case 'execution':
        return <Zap className="h-8 w-8 text-primary" />;
      case 'file':
        return <Database className="h-8 w-8 text-primary" />;
      case 'api':
        return <Wifi className="h-8 w-8 text-primary" />;
      default:
        return <Loader2 className="h-8 w-8 text-primary animate-spin" />;
    }
  };

  const getDefaultMessage = () => {
    switch (type) {
      case 'editor':
        return 'Loading editor...';
      case 'workspace':
        return 'Loading workspace...';
      case 'execution':
        return 'Executing code...';
      case 'file':
        return 'Loading files...';
      case 'api':
        return 'Connecting...';
      default:
        return 'Loading...';
    }
  };

  return (
    <div className={cn('flex flex-col items-center justify-center p-8 text-center', className)}>
      <div className="relative mb-4">
        {getIcon()}
        <div className="absolute -bottom-1 -right-1">
          <LoadingSpinner size="sm" />
        </div>
      </div>
      
      <h3 className="text-lg font-medium text-foreground mb-2">
        {message || getDefaultMessage()}
      </h3>
      
      {progress !== undefined && (
        <div className="w-full max-w-xs mb-2">
          <div className="flex justify-between text-sm text-muted-foreground mb-1">
            <span>Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-muted rounded-full h-2">
            <div
              className="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
            />
          </div>
        </div>
      )}
      
      <p className="text-sm text-muted-foreground max-w-md">
        {type === 'editor' && 'Initializing Monaco Editor and syntax highlighting...'}
        {type === 'workspace' && 'Setting up your coding environment...'}
        {type === 'execution' && 'Running your code and analyzing performance...'}
        {type === 'file' && 'Syncing your files and workspace data...'}
        {type === 'api' && 'Establishing secure connection to servers...'}
      </p>
    </div>
  );
}

interface ProgressBarProps {
  progress: number;
  label?: string;
  showPercentage?: boolean;
  className?: string;
}

export function ProgressBar({ 
  progress, 
  label, 
  showPercentage = true, 
  className 
}: ProgressBarProps) {
  const clampedProgress = Math.min(100, Math.max(0, progress));
  
  return (
    <div className={cn('w-full', className)}>
      {(label || showPercentage) && (
        <div className="flex justify-between text-sm text-muted-foreground mb-2">
          {label && <span>{label}</span>}
          {showPercentage && <span>{Math.round(clampedProgress)}%</span>}
        </div>
      )}
      <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
        <div
          className="bg-primary h-full rounded-full transition-all duration-300 ease-out"
          style={{ width: `${clampedProgress}%` }}
        />
      </div>
    </div>
  );
}

interface PulsingDotProps {
  className?: string;
}

export function PulsingDot({ className }: PulsingDotProps) {
  return (
    <div className={cn('flex items-center space-x-1', className)}>
      <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
      <div className="w-2 h-2 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.2s' }} />
      <div className="w-2 h-2 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.4s' }} />
    </div>
  );
}

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  progress?: number;
  className?: string;
}

export function LoadingOverlay({ 
  isVisible, 
  message = 'Loading...', 
  progress,
  className 
}: LoadingOverlayProps) {
  if (!isVisible) return null;

  return (
    <div className={cn(
      'absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50',
      className
    )}>
      <div className="bg-card border rounded-lg p-6 shadow-lg max-w-sm w-full mx-4">
        <div className="flex flex-col items-center text-center">
          <LoadingSpinner size="lg" className="mb-4" />
          <h3 className="text-lg font-medium mb-2">{message}</h3>
          {progress !== undefined && (
            <ProgressBar progress={progress} className="w-full" />
          )}
        </div>
      </div>
    </div>
  );
}

// Specialized loading components

export function EditorLoadingState() {
  return (
    <LoadingState 
      type="editor" 
      message="Initializing Code Editor"
      className="h-64"
    />
  );
}

export function WorkspaceLoadingState() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: 6 }).map((_, i) => (
        <LoadingCard key={i} />
      ))}
    </div>
  );
}

export function FileTreeLoadingState() {
  return (
    <div className="space-y-2 p-4">
      {Array.from({ length: 8 }).map((_, i) => (
        <div key={i} className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-muted rounded animate-pulse" />
          <div className={cn(
            'h-4 bg-muted rounded animate-pulse',
            i % 3 === 0 ? 'w-24' : i % 3 === 1 ? 'w-32' : 'w-20'
          )} />
        </div>
      ))}
    </div>
  );
}

export function CodeExecutionLoadingState({ progress }: { progress?: number }) {
  return (
    <div className="flex items-center justify-center p-8">
      <div className="text-center">
        <div className="relative mb-4">
          <Zap className="h-12 w-12 text-primary mx-auto" />
          <div className="absolute -top-1 -right-1">
            <LoadingSpinner size="sm" />
          </div>
        </div>
        <h3 className="text-lg font-medium mb-2">Executing Code</h3>
        {progress !== undefined && (
          <ProgressBar progress={progress} label="Progress" className="w-64" />
        )}
        <p className="text-sm text-muted-foreground mt-2">
          Running your code and analyzing performance...
        </p>
      </div>
    </div>
  );
}
