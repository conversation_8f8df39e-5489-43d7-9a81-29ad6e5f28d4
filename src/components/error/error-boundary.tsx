'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  RefreshCw, 
  Bug, 
  Copy, 
  ExternalLink,
  ChevronDown,
  ChevronUp 
} from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showDetails: boolean;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Call custom error handler
    this.props.onError?.(error, errorInfo);

    // Log to external service in production
    if (process.env.NODE_ENV === 'production') {
      this.logErrorToService(error, errorInfo);
    }
  }

  private logErrorToService(error: Error, errorInfo: ErrorInfo) {
    // In a real app, you'd send this to your error tracking service
    // like Sentry, LogRocket, or Bugsnag
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    console.log('Error logged:', errorData);
    // fetch('/api/errors', { method: 'POST', body: JSON.stringify(errorData) });
  }

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
    });
  };

  private handleReload = () => {
    window.location.reload();
  };

  private copyErrorDetails = () => {
    const { error, errorInfo } = this.state;
    const errorText = `
Error: ${error?.message}
Stack: ${error?.stack}
Component Stack: ${errorInfo?.componentStack}
URL: ${window.location.href}
User Agent: ${navigator.userAgent}
Timestamp: ${new Date().toISOString()}
    `.trim();

    navigator.clipboard.writeText(errorText).then(() => {
      console.log('Error details copied to clipboard');
    });
  };

  private reportIssue = () => {
    const { error } = this.state;
    const title = encodeURIComponent(`Bug Report: ${error?.message || 'Unknown Error'}`);
    const body = encodeURIComponent(`
**Error Description:**
${error?.message || 'Unknown error occurred'}

**Steps to Reproduce:**
1. 
2. 
3. 

**Expected Behavior:**


**Actual Behavior:**


**Additional Context:**
- URL: ${window.location.href}
- Browser: ${navigator.userAgent}
- Timestamp: ${new Date().toISOString()}

**Error Stack:**
\`\`\`
${error?.stack || 'No stack trace available'}
\`\`\`
    `);

    const issueUrl = `https://github.com/your-repo/codeable/issues/new?title=${title}&body=${body}`;
    window.open(issueUrl, '_blank');
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { error, errorInfo, showDetails } = this.state;

      return (
        <div className="min-h-screen flex items-center justify-center p-4 bg-muted/30">
          <Card className="w-full max-w-2xl">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-destructive/10 rounded-lg">
                  <AlertTriangle className="h-6 w-6 text-destructive" />
                </div>
                <div>
                  <CardTitle className="text-xl">Something went wrong</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    We encountered an unexpected error. Don't worry, your work is likely still saved.
                  </p>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Error Summary */}
              <div className="p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Bug className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Error Details</span>
                  <Badge variant="outline" className="text-xs">
                    {error?.name || 'Error'}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {error?.message || 'An unknown error occurred'}
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-2">
                <Button onClick={this.handleRetry} className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4" />
                  Try Again
                </Button>
                <Button variant="outline" onClick={this.handleReload}>
                  Reload Page
                </Button>
                <Button variant="outline" onClick={this.copyErrorDetails}>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Details
                </Button>
                <Button variant="outline" onClick={this.reportIssue}>
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Report Issue
                </Button>
              </div>

              {/* Technical Details (Collapsible) */}
              <div className="border-t pt-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => this.setState({ showDetails: !showDetails })}
                  className="flex items-center gap-2 text-sm"
                >
                  {showDetails ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                  Technical Details
                </Button>

                {showDetails && (
                  <div className="mt-3 space-y-3">
                    {error?.stack && (
                      <div>
                        <h4 className="text-sm font-medium mb-2">Stack Trace:</h4>
                        <pre className="text-xs bg-muted p-3 rounded overflow-auto max-h-40">
                          {error.stack}
                        </pre>
                      </div>
                    )}

                    {errorInfo?.componentStack && (
                      <div>
                        <h4 className="text-sm font-medium mb-2">Component Stack:</h4>
                        <pre className="text-xs bg-muted p-3 rounded overflow-auto max-h-40">
                          {errorInfo.componentStack}
                        </pre>
                      </div>
                    )}

                    <div>
                      <h4 className="text-sm font-medium mb-2">Environment:</h4>
                      <div className="text-xs space-y-1 text-muted-foreground">
                        <div>URL: {window.location.href}</div>
                        <div>User Agent: {navigator.userAgent}</div>
                        <div>Timestamp: {new Date().toISOString()}</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Help Text */}
              <div className="text-xs text-muted-foreground bg-muted/50 p-3 rounded">
                <p>
                  If this error persists, try refreshing the page or clearing your browser cache. 
                  Your workspace data is automatically saved and should be available when you return.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}
