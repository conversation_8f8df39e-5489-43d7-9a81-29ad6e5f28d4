'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Wifi, 
  WifiOff, 
  CloudOff, 
  RefreshCw, 
  AlertTriangle,
  CheckCircle,
  Clock,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface OfflineIndicatorProps {
  className?: string;
  showDetails?: boolean;
}

export function OfflineIndicator({ className, showDetails = false }: OfflineIndicatorProps) {
  const [isOnline, setIsOnline] = useState(true);
  const [showOfflineMessage, setShowOfflineMessage] = useState(false);
  const [lastOnlineTime, setLastOnlineTime] = useState<Date | null>(null);
  const [pendingActions, setPendingActions] = useState<string[]>([]);

  useEffect(() => {
    // Set initial state
    setIsOnline(navigator.onLine);

    const handleOnline = () => {
      setIsOnline(true);
      setShowOfflineMessage(false);
      console.log('Connection restored');
      
      // Show brief success message
      setTimeout(() => {
        setShowOfflineMessage(false);
      }, 3000);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setLastOnlineTime(new Date());
      setShowOfflineMessage(true);
      console.log('Connection lost');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Simulate pending actions (in a real app, this would come from a queue)
  useEffect(() => {
    if (!isOnline) {
      const actions = [
        'Save workspace changes',
        'Sync file modifications',
        'Upload code execution results',
      ];
      setPendingActions(actions);
    } else {
      setPendingActions([]);
    }
  }, [isOnline]);

  const formatOfflineTime = () => {
    if (!lastOnlineTime) return '';
    
    const now = new Date();
    const diffMs = now.getTime() - lastOnlineTime.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffSecs = Math.floor((diffMs % 60000) / 1000);
    
    if (diffMins > 0) {
      return `${diffMins}m ${diffSecs}s ago`;
    }
    return `${diffSecs}s ago`;
  };

  const handleRetryConnection = () => {
    // Force a network check
    if (navigator.onLine) {
      setIsOnline(true);
      setShowOfflineMessage(false);
    } else {
      // Try to ping a reliable endpoint
      fetch('/api/health', { method: 'HEAD', cache: 'no-cache' })
        .then(() => {
          setIsOnline(true);
          setShowOfflineMessage(false);
        })
        .catch(() => {
          console.log('Still offline');
        });
    }
  };

  if (!showDetails) {
    // Simple indicator
    return (
      <Badge
        variant={isOnline ? 'default' : 'destructive'}
        className={cn('flex items-center gap-2', className)}
      >
        {isOnline ? (
          <>
            <Wifi className="h-3 w-3" />
            Online
          </>
        ) : (
          <>
            <WifiOff className="h-3 w-3" />
            Offline
          </>
        )}
      </Badge>
    );
  }

  // Detailed offline message
  if (!isOnline && showOfflineMessage) {
    return (
      <Card className={cn('border-destructive bg-destructive/5', className)}>
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-destructive/10 rounded-lg">
              <CloudOff className="h-5 w-5 text-destructive" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-foreground">
                  You're offline
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowOfflineMessage(false)}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
              
              <p className="text-sm text-muted-foreground mb-3">
                Your internet connection was lost {formatOfflineTime()}. 
                Don't worry - your work is being saved locally and will sync when you're back online.
              </p>

              {pendingActions.length > 0 && (
                <div className="mb-3">
                  <h4 className="text-xs font-medium text-muted-foreground mb-2 flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    Pending actions ({pendingActions.length})
                  </h4>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    {pendingActions.map((action, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-orange-500 rounded-full" />
                        {action}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleRetryConnection}
                  className="text-xs"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Retry Connection
                </Button>
                <Badge variant="outline" className="text-xs">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  Limited functionality
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Connection restored message
  if (isOnline && showOfflineMessage) {
    return (
      <Card className={cn('border-green-500 bg-green-50 dark:bg-green-950/20', className)}>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                Connection restored
              </h3>
              <p className="text-sm text-green-600 dark:text-green-300">
                You're back online. Syncing your changes now...
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowOfflineMessage(false)}
              className="h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return null;
}

// Hook for offline detection
export function useOfflineDetection() {
  const [isOnline, setIsOnline] = useState(true);
  const [wasOffline, setWasOffline] = useState(false);

  useEffect(() => {
    setIsOnline(navigator.onLine);

    const handleOnline = () => {
      setIsOnline(true);
      if (wasOffline) {
        // Connection was restored
        console.log('Connection restored, syncing data...');
        // Trigger any pending sync operations here
      }
      setWasOffline(false);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setWasOffline(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [wasOffline]);

  return {
    isOnline,
    wasOffline,
    isReconnecting: wasOffline && isOnline,
  };
}

// Offline queue for actions
class OfflineQueue {
  private queue: Array<{ id: string; action: () => Promise<any>; timestamp: Date }> = [];
  private isProcessing = false;

  add(id: string, action: () => Promise<any>) {
    this.queue.push({ id, action, timestamp: new Date() });
    console.log(`Added action to offline queue: ${id}`);
  }

  async processQueue() {
    if (this.isProcessing || this.queue.length === 0) return;

    this.isProcessing = true;
    console.log(`Processing ${this.queue.length} queued actions...`);

    while (this.queue.length > 0) {
      const item = this.queue.shift()!;
      try {
        await item.action();
        console.log(`Successfully processed queued action: ${item.id}`);
      } catch (error) {
        console.error(`Failed to process queued action ${item.id}:`, error);
        // Re-queue if it's a network error
        if (error instanceof Error && error.message.includes('network')) {
          this.queue.unshift(item);
          break;
        }
      }
    }

    this.isProcessing = false;
  }

  getQueueSize() {
    return this.queue.length;
  }

  clear() {
    this.queue = [];
  }
}

export const offlineQueue = new OfflineQueue();

// Auto-process queue when connection is restored
if (typeof window !== 'undefined') {
  window.addEventListener('online', () => {
    setTimeout(() => offlineQueue.processQueue(), 1000);
  });
}
