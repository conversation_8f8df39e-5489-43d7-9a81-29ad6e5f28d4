import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { AuthProvider } from '@/contexts/auth-context';
import { ThemeProvider } from '@/contexts/theme-context';
import { PerformanceProvider } from '@/components/providers/performance-provider';
import { ErrorBoundary } from '@/components/error/error-boundary';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Codeable - AI-Powered Coding Practice Platform',
  description:
    'Practice coding problems and prepare for interviews with our professional coding environment.',
  manifest: '/manifest.json',
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0a0a0a' },
  ],
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'Codeable',
  },
  formatDetection: {
    telephone: false,
  },
  openGraph: {
    type: 'website',
    siteName: 'Codeable',
    title: 'Codeable - Coding Interview Platform',
    description: 'Professional coding interview preparation with advanced editor and complexity analysis',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Codeable - Coding Interview Platform',
    description: 'Professional coding interview preparation with advanced editor and complexity analysis',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="h-full">
      <head>
        {/* PWA Meta Tags */}
        <meta name="application-name" content="Codeable" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Codeable" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
        <meta name="msapplication-TileColor" content="#3A29FF" />
        <meta name="msapplication-tap-highlight" content="no" />

        {/* Apple Touch Icons */}
        <link rel="apple-touch-icon" href="/icon-152x152.png" />
        <link rel="apple-touch-icon" sizes="152x152" href="/icon-152x152.png" />
        <link rel="apple-touch-icon" sizes="180x180" href="/icon-180x180.png" />

        {/* Favicon */}
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="shortcut icon" href="/favicon.ico" />

        {/* Preload critical resources */}
        <link rel="preload" href="/fonts/geist-sans.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
        <link rel="preload" href="/fonts/geist-mono.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />

        {/* DNS Prefetch for external resources */}
        <link rel="dns-prefetch" href="//cdn.jsdelivr.net" />
        <link rel="preconnect" href="//cdn.jsdelivr.net" crossOrigin="anonymous" />

        {/* Theme initialization to prevent flash */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                function applyTheme(theme) {
                  const root = document.documentElement;
                  if (theme === 'dark') {
                    root.classList.add('dark');
                    root.style.colorScheme = 'dark';
                  } else if (theme === 'light') {
                    root.classList.remove('dark');
                    root.style.colorScheme = 'light';
                  } else {
                    // System theme
                    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                    if (prefersDark) {
                      root.classList.add('dark');
                      root.style.colorScheme = 'dark';
                    } else {
                      root.classList.remove('dark');
                      root.style.colorScheme = 'light';
                    }
                  }
                }

                // Apply saved theme immediately
                const savedTheme = localStorage.getItem('theme') || 'system';
                applyTheme(savedTheme);
              })();
            `,
          }}
        />

        {/* Smart Pyodide Loading - Only load when needed */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.pyodideLoading = false;
              window.pyodideLoadRequested = false;

              // Smart loader - only loads when Python is needed
              window.loadPyodideOnDemand = async function() {
                if (window.pyodide) return window.pyodide;
                if (window.pyodideLoading) return null;
                if (window.pyodideLoadRequested) return null;

                window.pyodideLoadRequested = true;
                window.pyodideLoading = true;

                try {
                  console.log('🐍 Loading Pyodide on demand...');

                  // Load Pyodide script if not already loaded
                  if (typeof loadPyodide === 'undefined') {
                    const script = document.createElement('script');
                    script.src = 'https://cdn.jsdelivr.net/pyodide/v0.24.1/full/pyodide.js';
                    document.head.appendChild(script);

                    await new Promise((resolve, reject) => {
                      script.onload = resolve;
                      script.onerror = reject;
                    });
                  }

                  // Initialize Pyodide
                  window.pyodide = await loadPyodide({
                    indexURL: "https://cdn.jsdelivr.net/pyodide/v0.24.1/full/"
                  });

                  console.log('✅ Pyodide loaded successfully!');
                  window.pyodideLoading = false;
                  return window.pyodide;
                } catch (error) {
                  console.error('❌ Failed to load Pyodide:', error);
                  window.pyodideLoading = false;
                  window.pyodideLoadRequested = false;
                  throw error;
                }
              };
            `,
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased h-full`}
      >
        <ErrorBoundary>
          <AuthProvider>
            <ThemeProvider>
              <PerformanceProvider>
                {children}
              </PerformanceProvider>
            </ThemeProvider>
          </AuthProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
