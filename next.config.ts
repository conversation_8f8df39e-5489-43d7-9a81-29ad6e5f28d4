import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Server external packages
  serverExternalPackages: ['appwrite', 'node-appwrite'],

  // Enable experimental features for better performance
  experimental: {
    // Enable optimized package imports
    optimizePackageImports: ['@monaco-editor/react', 'lucide-react'],
  },

  // Optimize images
  images: {
    domains: [],
    formats: ['image/webp', 'image/avif'],
  },

  // Environment variables validation
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // Webpack optimization for code splitting
  webpack: (config, { dev, isServer }) => {
    // Optimize bundle splitting
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          // Monaco Editor - large dependency
          monaco: {
            name: 'monaco',
            test: /[\\/]node_modules[\\/](@monaco-editor|monaco-editor)[\\/]/,
            priority: 30,
            reuseExistingChunk: true,
          },
          // Appwrite SDK
          appwrite: {
            name: 'appwrite',
            test: /[\\/]node_modules[\\/](appwrite|node-appwrite)[\\/]/,
            priority: 25,
            reuseExistingChunk: true,
          },
          // UI libraries
          ui: {
            name: 'ui',
            test: /[\\/]node_modules[\\/](@radix-ui|lucide-react)[\\/]/,
            priority: 20,
            reuseExistingChunk: true,
          },
          // React and core libraries
          react: {
            name: 'react',
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            priority: 15,
            reuseExistingChunk: true,
          },
          // Default vendor chunk for other node_modules
          vendor: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            reuseExistingChunk: true,
          },
        },
      };
    }

    // Optimize module resolution
    config.resolve.alias = {
      ...config.resolve.alias,
      // Reduce bundle size by using ES modules where possible
      lodash: 'lodash-es',
    };

    return config;
  },

  // Headers for security and performance
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
